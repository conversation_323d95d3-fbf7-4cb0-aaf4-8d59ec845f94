// demo.cpp : Defines the entry point for the console application.
//

#include "stdafx.h"
#include "LibTouchASUS.h"

bool g_bTerminate = false;
bool g_bStopTrigger = false;
//-------------------------------------------
// A function that polling device response
//-------------------------------------------
DWORD WINAPI CheckingThread( LPVOID lpParam ) 
{
	unsigned char cmdEnterGestureMode[] = {0x54, 0x40, 0x01, 0x01}; 	
	unsigned char cmdLeaveGestureMode[] = {0x54, 0x40, 0x00, 0x01}; 
	unsigned char cmdCheckGesture[] = {0x53, 0x88, 0x00, 0x01};
	int nRet = TP_SUCCESS;
	unsigned char Buf[20];
	memset(Buf, 0 , 20);	

	printf("\r\nEntering Gesture Mode.");
	nRet = ELANAPI::SendDevCommand(cmdEnterGestureMode, sizeof(cmdEnterGestureMode));
	if ( nRet != TP_SUCCESS ) {
		printf("\r\nSend cmdEnterGestureMode error : %s", ELANAPI::GetErrMsg() );
		return 2;
	}

	printf("\r\nDevice will assert wakeup pin plus if specified gesture is recognized.");
	Sleep ( 11000 ) ;	// For simulation of pc is entering sleep mode and wakeup.. simply using 10 sec.

	g_bTerminate = false;

	while (g_bStopTrigger == false ) {
		
		printf("\r\nChecking Gesture.");

		nRet = ELANAPI::SendDevCommand(cmdCheckGesture, sizeof(cmdCheckGesture));
		if ( nRet != TP_SUCCESS ) {
			printf("\r\nSend cmdCheckGesture error : %s", ELANAPI::GetErrMsg() );
			g_bTerminate = true;
			return 1;
		}

		nRet = ELANAPI::ReadDevData(Buf, 20);
		if ( nRet != TP_SUCCESS ) {
			printf( "\r\nReadDevData error : %s", ELANAPI::GetErrMsg() );
			g_bTerminate = true;
			return 2;
		}

		printf(" : %02X %02X %02X %02X", Buf[0], Buf[1], Buf[2], Buf[3]);
		if (( Buf[0] == 0x52) && ( Buf[1] == 0x88)) {	// The return data 1st byte should be 0x52
			printf("\r\nReceived gesture");
			g_bStopTrigger = true;
		}

		Sleep ( 500 ) ;
	}

	nRet = ELANAPI::SendDevCommand(cmdLeaveGestureMode, sizeof(cmdLeaveGestureMode));
	if ( nRet != TP_SUCCESS ) {
		printf("\r\nSend cmdLeaveGestureMode error : %s", ELANAPI::GetErrMsg() );
		return 1;
	}
	printf("\r\nLeaving Gesture Mode.");

	g_bTerminate = true;

	return 0;
}
int _tmain(int argc, _TCHAR* argv[])
{
	
	int nRet = TP_SUCCESS;
	int nDevCnt = 0;
	int	i = 0;

	unsigned char Buf[20];
	memset(Buf, 0 , 20);	

	nRet = ELANAPI::Connect(0x04F3, 0x00);

	if ( nRet != TP_SUCCESS ) {
		printf("\r\nConnect error : %s", ELANAPI::GetErrMsg() );
		return 1;
	}

	printf("\r\nConnect Success.");

	g_bTerminate = false;
	HANDLE hThread = INVALID_HANDLE_VALUE;

    hThread = CreateThread( NULL, 0, CheckingThread, NULL, 0, NULL);  
    if ( hThread == INVALID_HANDLE_VALUE) {
        //ExitProcess(CheckingThread);
		return 3;
	}

	while (g_bTerminate == false ) {
		Sleep(100);
	}

	printf("\r\nPolling stop.");

	if (hThread != INVALID_HANDLE_VALUE) {
		CloseHandle(hThread);
		hThread = INVALID_HANDLE_VALUE;
	}

	nRet = ELANAPI::Disconnect();
	if ( nRet != TP_SUCCESS ) {
		printf( "\r\nDisconnect error : %s", ELANAPI::GetErrMsg() );
		return 4;
	}
	printf("\r\nDevice disconnect.");
	system("PAUSE");

	return 0;
}

