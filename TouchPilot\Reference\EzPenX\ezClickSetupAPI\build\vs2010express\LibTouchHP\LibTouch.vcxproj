﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|X64">
      <Configuration>Debug</Configuration>
      <Platform>X64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|X64">
      <Configuration>Release</Configuration>
      <Platform>X64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{6279D234-266C-4C6E-B1C3-FB5B0DFACD5D}</ProjectGuid>
    <ProjectName>LibTouch</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>NotSet</CharacterSet>
    <PlatformToolset>v100</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>NotSet</CharacterSet>
    <PlatformToolset>v100</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|X64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|X64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Redirector Monitor Debug|X64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|X64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|X64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Redirector Monitor Debug|X64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.40219.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(SolutionDir)$(Configuration)\$(ProjectName)\Intermediate\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(SolutionDir)$(Configuration)\$(ProjectName)\Intermediate\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(ProjectName)D</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(ProjectName)</TargetName>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\Debug/ElanTPDLL.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;__ENABLE_DEBUG__;_DEBUG;_CRT_SECURE_NO_WARNINGS;_WINDOWS;_USRDLL;ELANTPDLL_EXPORTS;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <AdditionalIncludeDirectories>..\..\..\lib\windows\;..\..\..\src\CsvParam;..\..\..\include;..\..\..\include\windows\HP;..\..\..\lib\windows\muparser\include</AdditionalIncludeDirectories>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0404</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>User32.lib;odbc32.lib;odbccp32.lib;Hid.lib;Setupapi.lib;Winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <TargetMachine>MachineX86</TargetMachine>
      <AdditionalLibraryDirectories>..\..\..\lib\windows\i386;</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>
      </IgnoreSpecificDefaultLibraries>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/ElanTPDLL.bsc</OutputFile>
    </Bscmake>
    <PostBuildEvent>
      <Command>copy "$(OutDir)$(TargetName).lib" ..\..\..\bin\windows\HP\Lib
copy "$(OutDir)$(TargetName).dll" ..\..\..\bin\windows\HP\Lib
copy "..\..\..\include\windows\HP\LibTouchHP.h" ..\..\..\bin\windows\HP\Lib</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\Debug/ElanTPDLL.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CRT_SECURE_NO_WARNINGS;_WINDOWS;_USRDLL;ELANTPDLL_EXPORTS;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <AdditionalIncludeDirectories>..\..\..\lib\windows\;..\..\..\include;..\..\..\src\CsvParam;..\..\..\lib\windows\muparser\include;..\..\..\include\windows\Hp</AdditionalIncludeDirectories>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0404</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;Hid.lib;Setupapi.lib;Winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <TargetMachine>MachineX86</TargetMachine>
      <AdditionalLibraryDirectories>..\..\..\lib\windows\i386;</AdditionalLibraryDirectories>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/ElanTPDLL.bsc</OutputFile>
    </Bscmake>
    <PostBuildEvent>
      <Command>copy "$(OutDir)$(TargetName).lib" ..\..\..\bin\windows\HP\Lib
copy "$(OutDir)$(TargetName).dll" ..\..\..\bin\windows\HP\Lib
copy "..\..\..\include\windows\HP\LibTouchHP.h" ..\..\..\bin\windows\HP\Lib
</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|X64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Debug/ElanTPDLL.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;ELANTPDLL_EXPORTS;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>.\Debug/ElanTPDLL.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0404</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;Hid.lib;Setupapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/ElanTPDLL.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|X64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Debug/ElanTPDLL.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;ELANTPDLL_EXPORTS;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>.\Debug/ElanTPDLL.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0404</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;Hid.lib;Setupapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/ElanTPDLL.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Redirector Monitor Debug|X64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\Debug/ElanTPDLL.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;ELANTPDLL_EXPORTS;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>.\Debug/ElanTPDLL.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0404</Culture>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>odbc32.lib;odbccp32.lib;Hid.lib;Setupapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
    </Link>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/ElanTPDLL.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\lib\windows\muparser\src\muParser.cpp" />
    <ClCompile Include="..\..\..\lib\windows\muparser\src\muParserBase.cpp" />
    <ClCompile Include="..\..\..\lib\windows\muparser\src\muParserBytecode.cpp" />
    <ClCompile Include="..\..\..\lib\windows\muparser\src\muParserCallback.cpp" />
    <ClCompile Include="..\..\..\lib\windows\muparser\src\muParserDLL.cpp" />
    <ClCompile Include="..\..\..\lib\windows\muparser\src\muParserError.cpp" />
    <ClCompile Include="..\..\..\lib\windows\muparser\src\muParserInt.cpp" />
    <ClCompile Include="..\..\..\lib\windows\muparser\src\muParserTest.cpp" />
    <ClCompile Include="..\..\..\lib\windows\muparser\src\muParserTokenReader.cpp" />
    <ClCompile Include="..\..\..\src\AddrTblParam\ElanAddrTblParam.cpp" />
    <ClCompile Include="..\..\..\src\AddrTblParam\LibAddrTblParam.cpp" />
    <ClCompile Include="..\..\..\src\CsvParam\ElanCsvParam.cpp" />
    <ClCompile Include="..\..\..\src\CsvParam\LibCsvParam.cpp" />
    <ClCompile Include="..\..\..\src\FW20I2CLinuxGet.cpp" />
    <ClCompile Include="..\..\..\src\HIDLinuxGet.cpp" />
    <ClCompile Include="..\..\..\src\HIDWinGet.cpp" />
    <ClCompile Include="..\..\..\src\I2CChromeLinuxGet.cpp" />
    <ClCompile Include="..\..\..\src\I2CHIDLinuxGet.cpp" />
    <ClCompile Include="..\..\..\src\I2CLinuxGet.cpp" />
    <ClCompile Include="..\..\..\src\linux_debug_utility.cpp" />
    <ClCompile Include="..\..\..\src\SocketGet.cpp" />
    <ClCompile Include="..\..\..\src\TouchIC.cpp" />
    <ClCompile Include="..\..\..\src\windows\Hp\LibTouchHp.cpp" />
    <ClCompile Include="..\..\..\src\windows_debug_utility.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\include\BuildConfig.h" />
    <ClInclude Include="..\..\..\include\ektf3k_ioctl.h" />
    <ClInclude Include="..\..\..\include\ElanSocketLibrary.h" />
    <ClInclude Include="..\..\..\include\ErrCode.h" />
    <ClInclude Include="..\..\..\include\FW20I2CLinuxGet.h" />
    <ClInclude Include="..\..\..\include\HIDLinuxGet.h" />
    <ClInclude Include="..\..\..\include\HIDWinGet.h" />
    <ClInclude Include="..\..\..\include\I2CChromeLinuxGet.h" />
    <ClInclude Include="..\..\..\include\I2CHIDLinuxGet.h" />
    <ClInclude Include="..\..\..\include\I2CLinuxGet.h" />
    <ClInclude Include="..\..\..\include\InterfaceGet.h" />
    <ClInclude Include="..\..\..\include\LibTouchExp.h" />
    <ClInclude Include="..\..\..\include\linux_debug_utility.h" />
    <ClInclude Include="..\..\..\include\linux_win32_def.h" />
    <ClInclude Include="..\..\..\include\linux_win32_func_wrapper.h" />
    <ClInclude Include="..\..\..\include\SocketGet.h" />
    <ClInclude Include="..\..\..\include\TouchIC.h" />
    <ClInclude Include="..\..\..\include\windows\Hp\LibTouchHP.h" />
    <ClInclude Include="..\..\..\include\windows_debug_utility.h" />
    <ClInclude Include="..\..\..\lib\windows\muparser\include\muParser.h" />
    <ClInclude Include="..\..\..\lib\windows\muparser\include\muParserBase.h" />
    <ClInclude Include="..\..\..\lib\windows\muparser\include\muParserBytecode.h" />
    <ClInclude Include="..\..\..\lib\windows\muparser\include\muParserCallback.h" />
    <ClInclude Include="..\..\..\lib\windows\muparser\include\muParserDef.h" />
    <ClInclude Include="..\..\..\lib\windows\muparser\include\muParserDLL.h" />
    <ClInclude Include="..\..\..\lib\windows\muparser\include\muParserError.h" />
    <ClInclude Include="..\..\..\lib\windows\muparser\include\muParserFixes.h" />
    <ClInclude Include="..\..\..\lib\windows\muparser\include\muParserInt.h" />
    <ClInclude Include="..\..\..\lib\windows\muparser\include\muParserStack.h" />
    <ClInclude Include="..\..\..\lib\windows\muparser\include\muParserTemplateMagic.h" />
    <ClInclude Include="..\..\..\lib\windows\muparser\include\muParserTest.h" />
    <ClInclude Include="..\..\..\lib\windows\muparser\include\muParserToken.h" />
    <ClInclude Include="..\..\..\lib\windows\muparser\include\muParserTokenReader.h" />
    <ClInclude Include="..\..\..\src\AddrTblParam\ElanAddrTblParam.h" />
    <ClInclude Include="..\..\..\src\AddrTblParam\ElanAddrTblParamDef.h" />
    <ClInclude Include="..\..\..\src\AddrTblParam\ElanAddrTblParamErrorCode.h" />
    <ClInclude Include="..\..\..\src\AddrTblParam\LibAddrTblParam.h" />
    <ClInclude Include="..\..\..\src\CsvParam\ElanCsvParam.h" />
    <ClInclude Include="..\..\..\src\CsvParam\LibCsvParam.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>