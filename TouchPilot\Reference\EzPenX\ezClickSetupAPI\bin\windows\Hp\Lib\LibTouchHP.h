#ifndef _LIB_TOUCH_H_INCLUDE_948228FV_85JI_2964_IE94_058276KL4A52
#define _LIB_TOUCH_H_INCLUDE_948228FV_85JI_2964_IE94_058276KL4A52

// The following ifdef block is the standard way of creating macros which make exporting 
// from a DLL simpler. All files within this DLL are compiled with the ELANTPDLL_EXPORTS
// symbol defined on the command line. this symbol should not be defined on any project
// that uses this DLL. This way any other project whose source files include this file see 
// ELANTPDLL_API functions as being imported from a DLL, wheras this DLL sees symbols
// defined with this macro as being exported.


//////////////////////////////////////////////////////////////
// Error code definition
//////////////////////////////////////////////////////////////

//** Function execution is success. **/
#ifndef TP_SUCCESS
#define TP_SUCCESS					0x0000	
#endif
/** This function is not support. **/
#ifndef TP_ERR_COMMAND_NOT_SUPPORT
#define TP_ERR_COMMAND_NOT_SUPPORT  0x0001
#endif
/** The touch ic may be occupied by other application **/
#ifndef TP_ERR_DEVICE_BUSY
#define TP_ERR_DEVICE_BUSY			0x0002
#endif
//J2++
/** Read/Write data timeout **/
#ifndef TP_ERR_TIMEOUT
#define TP_ERR_TIMEOUT				0x0003
#endif
//J2--
/** For asynchorous call, the execution is not finish yet. Waitting for complete **/
#ifndef TP_ERR_IO_PENDING
#define TP_ERR_IO_PENDING			0x0004
#endif
/** For any read command, sometimes the return data should meet some rule. **/
#ifndef TP_ERR_DATA_PATTEN
#define TP_ERR_DATA_PATTEN			0x0005
#endif

//J2++
/** No interface create **/
#ifndef TP_ERR_NO_INTERFACE_CREATE
#define TP_ERR_NO_INTERFACE_CREATE	0x0006
#endif
//J2--

#ifndef TP_ERR_HID_NOT_SUPPORT
#define TP_ERR_HID_NOT_SUPPORT		0x0007
#endif

/** Connect Elan Bridge and not get hello packet **/
#ifndef TP_ERR_CONNECT_NO_HELLO_PACKEY
#define TP_ERR_CONNECT_NO_HELLO_PACKEY		0x1002
#endif
/** Did not find any support device connectted to PC. Please check connectoin. **/
#ifndef TP_ERR_NOT_FOUND_DEVICE
#define TP_ERR_NOT_FOUND_DEVICE				0x1004
#endif

/** Test Mode Error Code **/
#ifndef TP_TESTMODE_GET_RAWDATA_FAIL
#define TP_TESTMODE_GET_RAWDATA_FAIL		0x3001			
#endif 

/** CSV Parameter Error Code **/
#ifndef ERR_CSVFILE_NOT_EXIST
#define ERR_CSVFILE_NOT_EXIST				0x4001
#endif

#ifndef ERR_CSVFILE_OPEN_FAILURE
#define ERR_CSVFILE_OPEN_FAILURE			0x4002
#endif

#ifndef ERR_PARAM_FILE_INFO_INVALID
#define ERR_PARAM_FILE_INFO_INVALID			0x4003
#endif

#ifndef ERR_INIFILE_OPEN_FAILURE
#define ERR_INIFILE_OPEN_FAILURE			0x5001
#endif

#ifndef ERR_EEPROM_WRITE_FAILURE
#define ERR_EEPROM_WRITE_FAILURE			0x6001
#endif

/** Error information check, use GetErrMsg to get the error message. **/
#ifndef TP_ERR_CHK_MSG
#define TP_ERR_CHK_MSG						0xFFFF
#endif 

#pragma once
#include <Windows.h>
#include <string>
#include <vector>
#include <stdexcept>

using namespace std;


#ifdef ELANTPDLL_EXPORTS
#define ELANTPDLL_API extern "C" __declspec(dllexport) 
#else
#define ELANTPDLL_API extern "C" __declspec(dllimport)
#endif


////////////////////////////////////////////////////////////////////
// Since the pen report have different format by different firmware.
// The libraty will judge it first.
//

namespace ELANAPI {

/******************************************************************
* Elan TP Library												  *
*******************************************************************
* Ver 0.0.1														  *							  
*******************************************************************/
////////////////////////////////////////////////////////////////////
// The default format for touch finger and pen data format
//

////////////////////////////////////////////////////////
// Debug and Error message functions
////////////////////////////////////////////////////////

// When API function call get error code "TP_ERR_CHK_MSG". Use this 
// function to get more error information.
ELANTPDLL_API char* __stdcall GetErrMessage(void);

// Internal coding use only. For some specail tool, set this function to
// show more information at runtime.
ELANTPDLL_API void __stdcall SetMsgOutFunc(char* sTag, void (*pFuncDebugOutStream)(char* sOutMessage, int nMessageLen));

////////////////////////////////////////////////////////
//Declare functions : HID basic funcitons
////////////////////////////////////////////////////////

//nInterface
// Windows:
//	-INTF_TYPE_HID_WINDOWS			= 1;
//
// Linux:
//	-INTF_TYPE_HID_LINUX			= 2;
//	-INTF_TYPE_I2C_LINUX			= 3;
//	-INTF_TYPE_I2CHID_LINUX			= 4;
//	-INTF_TYPE_I2C_CHROME_LINUX		= 5;
//	-INTF_TYPE_FW20_I2C_LINUX		= 6;
ELANTPDLL_API int __stdcall Connect(int nVID = 0x04F3, 
									int nPID = 0x00, 
									int nInterface=1, //Default is Windows HID
									int nVdd=33, 
									int nVio=33, 
									int nI2CAdr=0x20,
									int nI2CLength=0x3f);

//ELANTPDLL_API int ConnectByDevPath(int nVID, int nPID, char *pszDevPath);

/*!
	Close the connection wiht HID Device
*/
ELANTPDLL_API int __stdcall Disconnect();

/*!
	Disable the TP Report
	@param[in] bEnable true to disable TP Report;false to enable TP Report
	@param[in] nDevIdx The HID Device Index, default is set to 0
 */
ELANTPDLL_API int __stdcall DisableTPReport(bool bDisable, int nTimeoutMS = 1000, int nDevIdx = 0);
ELANTPDLL_API int __stdcall DisableActivePen(bool bDisable, int nTimeoutMS = 1000, int nDevIdx = 0);

/*!
	Get the Algorithm
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return function result
*/
ELANTPDLL_API int __stdcall GetFWIPOption(int &nValue, int nTimeoutMS = 1000, int nDevIdx = 0);
ELANTPDLL_API int __stdcall SetFWIPOption(int nValue, int nTimeoutMS = 1000, int nDevIdx = 0);

} // namespace ELANAPI

#endif // _LIB_TOUCH_H_INCLUDE_948228FV_85JI_2964_IE94_058276KL4A52