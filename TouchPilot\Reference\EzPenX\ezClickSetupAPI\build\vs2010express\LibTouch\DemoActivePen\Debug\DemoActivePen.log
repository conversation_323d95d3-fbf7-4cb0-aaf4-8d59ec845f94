﻿Build started 2023/8/2 下午 03:27:04.
     1>Project "D:\workspace\Git_ScriptAgent\ScriptAgent\LibTouch_20230802\build\vs2010express\LibTouch\DemoActivePen\DemoActivePen.vcxproj" on node 2 (build target(s)).
     1>InitializeBuildStatus:
         Creating "Debug\DemoActivePen.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /I..\..\..\..\bin\windows\Elan /ZI /nologo /W3 /WX- /Od /Oy- /D WIN32 /D _DEBUG /D _WINDOWS /Gm /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Yc"StdAfx.h" /Fp"Debug\DemoActivePen.pch" /Fo"Debug\\" /Fd"Debug\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt ..\..\..\..\demo\windows_ActivePen\ActivePenDemo.cpp ..\..\..\..\demo\windows_ActivePen\stdafx.cpp
         stdafx.cpp
         ActivePenDemo.cpp
     1>d:\workspace\git_scriptagent\scriptagent\libtouch_20230802\demo\windows_activepen\activependemo.cpp(258): error C2228: left of '.ReportID' must have class/struct/union
                 type is 'FINGER_PROFILE [4]'
     1>d:\workspace\git_scriptagent\scriptagent\libtouch_20230802\demo\windows_activepen\activependemo.cpp(258): error C2664: 'int sprintf_s(char *,size_t,const char *,...)' : cannot convert parameter 2 from 'const char [27]' to 'size_t'
                 There is no context in which this conversion is possible
     1>d:\workspace\git_scriptagent\scriptagent\libtouch_20230802\demo\windows_activepen\activependemo.cpp(261): error C2228: left of '.inputReportLength' must have class/struct/union
                 type is 'FINGER_PROFILE [4]'
     1>d:\workspace\git_scriptagent\scriptagent\libtouch_20230802\demo\windows_activepen\activependemo.cpp(261): error C2664: 'int sprintf_s(char *,size_t,const char *,...)' : cannot convert parameter 2 from 'const char [31]' to 'size_t'
                 There is no context in which this conversion is possible
     1>d:\workspace\git_scriptagent\scriptagent\libtouch_20230802\demo\windows_activepen\activependemo.cpp(265): error C2228: left of '.maxLogicalX' must have class/struct/union
                 type is 'FINGER_PROFILE [4]'
     1>d:\workspace\git_scriptagent\scriptagent\libtouch_20230802\demo\windows_activepen\activependemo.cpp(265): error C2228: left of '.maxLogicalY' must have class/struct/union
                 type is 'FINGER_PROFILE [4]'
     1>d:\workspace\git_scriptagent\scriptagent\libtouch_20230802\demo\windows_activepen\activependemo.cpp(265): error C2664: 'int sprintf_s(char *,size_t,const char *,...)' : cannot convert parameter 2 from 'const char [31]' to 'size_t'
                 There is no context in which this conversion is possible
     1>Done Building Project "D:\workspace\Git_ScriptAgent\ScriptAgent\LibTouch_20230802\build\vs2010express\LibTouch\DemoActivePen\DemoActivePen.vcxproj" (build target(s)) -- FAILED.

Build FAILED.

Time Elapsed 00:00:00.53
