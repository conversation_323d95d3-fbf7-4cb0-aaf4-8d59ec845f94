# NOTE: Requires **VS2019 16.7** or later

# 'MicrosoftCodeAnalysisDesign' Rules from '2.9.8' release with 'Minimum' analysis mode
# Description: 'MicrosoftCodeAnalysisDesign' Rules with enabled-by-default state from '2.9.8' release with 'Minimum' analysis mode. Rules that are first released in a version later than '2.9.8' are disabled.

is_global = true

global_level = -99


# RS1029: Do not use reserved diagnostic IDs
dotnet_diagnostic.RS1029.severity = none

# RS1031: Define diagnostic title correctly
dotnet_diagnostic.RS1031.severity = none

# RS1032: Define diagnostic message correctly
dotnet_diagnostic.RS1032.severity = none

# RS1033: Define diagnostic description correctly
dotnet_diagnostic.RS1033.severity = none

# RS1037: Add "CompilationEnd" custom tag to compilation end diagnostic descriptor
dotnet_diagnostic.RS1037.severity = none
