# NOTE: Requires **VS2019 16.7** or later

# 'MicrosoftCodeAnalysisCorrectness' Rules from '3.0.0' release with 'Default' analysis mode escalated to 'error' severity
# Description: 'MicrosoftCodeAnalysisCorrectness' Rules with enabled-by-default state from '3.0.0' release with 'Default' analysis mode. Rules that are first released in a version later than '3.0.0' are disabled. Enabled rules with 'warning' severity are escalated to 'error' severity to respect 'CodeAnalysisTreatWarningsAsErrors' MSBuild property.

is_global = true

global_level = -99


# RS1001: Missing diagnostic analyzer attribute
dotnet_diagnostic.RS1001.severity = error

# RS1002: Missing kind argument when registering an analyzer action
dotnet_diagnostic.RS1002.severity = error

# RS1003: Unsupported SymbolKind argument when registering a symbol analyzer action
dotnet_diagnostic.RS1003.severity = error

# RS1004: Recommend adding language support to diagnostic analyzer
dotnet_diagnostic.RS1004.severity = error

# RS1005: ReportDiagnostic invoked with an unsupported DiagnosticDescriptor
dotnet_diagnostic.RS1005.severity = error

# RS1006: Invalid type argument for DiagnosticAnal<PERSON>zer's Register method
dotnet_diagnostic.RS1006.severity = error

# RS1014: Do not ignore values returned by methods on immutable objects
dotnet_diagnostic.RS1014.severity = error

# RS1022: Do not use types from Workspaces assembly in an analyzer
dotnet_diagnostic.RS1022.severity = error

# RS1024: Symbols should be compared for equality
dotnet_diagnostic.RS1024.severity = error

# RS1025: Configure generated code analysis
dotnet_diagnostic.RS1025.severity = error

# RS1026: Enable concurrent execution
dotnet_diagnostic.RS1026.severity = error

# RS1027: Types marked with DiagnosticAnalyzerAttribute(s) should inherit from DiagnosticAnalyzer
dotnet_diagnostic.RS1027.severity = error

# RS1030: Do not invoke Compilation.GetSemanticModel() method within a diagnostic analyzer
dotnet_diagnostic.RS1030.severity = error

# RS1035: Do not use APIs banned for analyzers
dotnet_diagnostic.RS1035.severity = none

# RS1036: Specify analyzer banned API enforcement setting
dotnet_diagnostic.RS1036.severity = none
