// demo.cpp : Defines the entry point for the console application.
//

#include "stdafx.h"
#include "LibTouchHP.h"


enum EXE_CMD_TYPE{  EXE_DISABLE_TP, EXE_ENABLE_TP, EXE_DISABLE_PEN, EXE_ENABLE_PEN};

unsigned char* ReportData = NULL;

int Execution( EXE_CMD_TYPE cmdType );

void DebugShow(char* sDbgMessage, int strLen)
{
	printf_s("\r\n");
	printf_s(sDbgMessage);
}

void HelpShow()
{
	printf_s("\r\n[ Parameter Description ]\r\n");
	printf_s("-h        Help document\r\n");
	printf_s("-tp=off   Touch Panel Report Disable\r\n");
	printf_s("-tp=on    Touch Panel Report Enable\r\n");
	printf_s("-pen=off  Active Pen Report Disable\r\n");
	printf_s("-pen=on   Active Pen Report Enable\r\n");

	printf_s("The parameter are case sensitive. e.q TpCtrl.exe -tp=off ");
}

int _tmain(int argc, _TCHAR* argv[])
{
	int nRet = TP_SUCCESS;
	int nDevCnt = 0;
	int	i = 0;
	unsigned int nFWID = 0;
	unsigned int nFWVer = 0;
	unsigned int nVID = 0;
	unsigned int nPID = 0;	

	for ( i = 0; i < argc; i ++ ) {
		if ((argc ==1) || ( _tcscmp( argv[i], _T("-h")) == 0 )) {
			HelpShow();
			break;
		}

		if ( _tcscmp( argv[i], _T("-d")) == 0 ) {
			ELANAPI::SetMsgOutFunc(NULL, DebugShow);
		}

		if ( _tcscmp( argv[i], _T("-tp=off")) == 0 ) {
			Execution( EXE_DISABLE_TP );
		}

		if ( _tcscmp( argv[i], _T("-tp=on")) == 0 ) {
			Execution( EXE_ENABLE_TP );
		}
		
		if ( _tcscmp( argv[i], _T("-pen=off")) == 0 ) {
			Execution( EXE_DISABLE_PEN );
		}

		if ( _tcscmp( argv[i], _T("-pen=on")) == 0 ) {
			Execution( EXE_ENABLE_PEN );
		}
	}


	return 0;
}


int Execution( EXE_CMD_TYPE cmdType )
{
	int nRet = TP_SUCCESS;
	int i = 0;
	unsigned int nVID = 0;
	unsigned int nPID = 0;
	unsigned int nFWID = 0;
	unsigned int nFWVer = 0;

	int nRXTrace = 0; 
	int nTXTrace = 0; 


	nRet = ELANAPI::Connect(0x04F3, 0x00);

	if ( nRet != TP_SUCCESS ) {
		printf( ELANAPI::GetErrMessage() );
		return 0;
	}

	//for(i = 0; i < ELANAPI::GetDeviceCount() ; i++) {
	switch ( cmdType ) {
	case EXE_DISABLE_TP:
		nRet = ELANAPI::DisableTPReport(true);
		if ( nRet != TP_SUCCESS ) {
			goto EXIT_EXECUTION;
		}
		break;

	case EXE_ENABLE_TP:
		nRet = ELANAPI::DisableTPReport(false);
		if ( nRet != TP_SUCCESS ) {
			goto EXIT_EXECUTION;
		}
		break;
	case EXE_DISABLE_PEN:
		nRet = ELANAPI::DisableActivePen(true);
		if ( nRet != TP_SUCCESS ) {
			goto EXIT_EXECUTION;
		}
		break;
	case EXE_ENABLE_PEN:
		nRet = ELANAPI::DisableActivePen(false);
		if ( nRet != TP_SUCCESS ) {
			goto EXIT_EXECUTION;
		}
		break;
	default:
		break;
	}

EXIT_EXECUTION: 
		nRet = ELANAPI::Disconnect();
		if ( nRet != TP_SUCCESS ) {
			printf( ELANAPI::GetErrMessage() );
		}

		if ( nRet != TP_SUCCESS ) {
			printf( "\r\nDevice(%d) error : %s", i, ELANAPI::GetErrMessage() );
		}
		
		return nRet;
}