// ActivePenDemo.cpp : Defines the entry point for the application.
//

#include "stdafx.h"
#include "TpCtrl.h"
#include <windows.h>
#include <wtypes.h>
#include "LibTouchHP.h"

//****************************************************************************
// Global module variables
//****************************************************************************
static HINSTANCE          hGInstance; //global application instance handle
static HWND				  hGOutWinCtrl = NULL;

enum EXE_CMD_TYPE{  EXE_DISABLE_TP, EXE_ENABLE_TP, EXE_DISABLE_PEN, EXE_ENABLE_PEN};

//****************************************************************************
// UI FUNCTIONS
//****************************************************************************
INT_PTR CALLBACK bMainDlgProc( HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam);
void DebugShow(char* sDbgMessage, int strLen);
void DebugShow(char* sDbgMessage);

//****************************************************************************
// TOUCH DEVICE FUNCTIONS
//****************************************************************************
int Execution( EXE_CMD_TYPE cmdType );

//****************************************************************************
// Windows message handler function
//****************************************************************************
void MSGCommandHandler(HWND hDlg, WPARAM wParam, LPARAM lParam);

//****************************************************************************
// Global variables
//****************************************************************************
bool	g_bFingerCtrlEnable = TRUE;
bool	g_bPenCtrlEnable = TRUE;


/////////////////////////////////////////////////////////////////////////
//
//


/*******************************
*WinMain: Windows Entry point  *
*******************************/
int APIENTRY _tWinMain(HINSTANCE hInstance,
                     HINSTANCE hPrevInstance,
                     LPTSTR    lpCmdLine,
                     int       nCmdShow)
{
	UNREFERENCED_PARAMETER(hPrevInstance);
    UNREFERENCED_PARAMETER(lpCmdLine);
    UNREFERENCED_PARAMETER(nCmdShow);

    LPWSTR  *szArgList = NULL;

    //
    // Save instance of the application for further reference
    //
    hGInstance = hInstance;

	if (-1 == DialogBox(hInstance, _T("MAIN_DIALOG"), NULL, bMainDlgProc)) {
		MessageBox(NULL, _T("Unable to create root dialog!"), _T("DialogBox failure"), MB_ICONSTOP);
	}

    if (NULL != szArgList)
    {
        LocalFree(szArgList);
        szArgList = NULL;
    }

    return (0);
}

/*************************************************
 * Main Dialog proc                              *
 *************************************************/
INT_PTR CALLBACK 
bMainDlgProc(
    HWND hDlg, 
    UINT message, 
    WPARAM wParam, 
    LPARAM lParam
)
{
    BOOL    status = FALSE;
	int		nRet = TP_SUCCESS;
	char	sOut[512] = {'\0'};
	char    sTmp[512] = {'\0'};
	int		nSysRet = 0;
	int		nScrX = 0, nScrY = 0;
    switch (message)
    {
        case WM_INITDIALOG:

			hGOutWinCtrl = GetDlgItem(hDlg,IDC_ITEMS);

			nScrX = (GetSystemMetrics(SM_CXSCREEN)/2) - DIALOG_WIDTH;
			nScrY = (GetSystemMetrics(SM_CYSCREEN)/2) - DIALOG_HEIGHT;

			::SetWindowPos(hDlg,  NULL, nScrX, nScrY, 0, 0, SWP_SHOWWINDOW|SWP_NOSIZE/*|SWP_NOMOVE*/);
			CheckDlgButton(hDlg, IDC_CHKBOX_FINGER_REPORT, BST_CHECKED);
			CheckDlgButton(hDlg, IDC_CHKBOX_PEN_REPORT, BST_CHECKED);
            break; // end WM_INITDIALOG case	

		case WM_SETTINGCHANGE:

			// Reflects the state of the laptop or slate mode, 0 for Slate Mode and non-zero otherwise. 
			nSysRet = GetSystemMetrics(0x2003 /*SM_CONVERTILBLESLATEMODE*/);
			sprintf_s(sTmp, "\r\nGetSystemMetrics return %d:", nSysRet);
			DebugShow(sTmp, strlen(sTmp));

			//If the return value is 0, turn on the touchscreen and pen, else turn off what is checked.
			if ( nSysRet == 0 ) {				
				// turn on the touchscreen and pen
				if ( g_bFingerCtrlEnable == TRUE ) {
					Execution( EXE_ENABLE_TP );
					sprintf_s(sTmp, "\r\nTrun on TP Finger Report");
					DebugShow(sTmp, strlen(sTmp));
				}
				if ( g_bPenCtrlEnable == TRUE ) {
					Execution( EXE_ENABLE_PEN );
					sprintf_s(sTmp, "\r\nTrun on TP Pen Report");
					DebugShow(sTmp, strlen(sTmp));
				}
			}
			else {
				// turn off the touchscreen and pen
				if ( g_bFingerCtrlEnable  == TRUE ) {
					Execution( EXE_DISABLE_TP );
					sprintf_s(sTmp, "\r\nTrun off TP Finger Report");
					DebugShow(sTmp, strlen(sTmp));
					//g_bFingerReportON = FALSE;
				}

				if ( g_bPenCtrlEnable == TRUE ) {
					Execution( EXE_DISABLE_PEN );
					sprintf_s(sTmp, "\r\nTrun off TP Pen Report");
					DebugShow(sTmp, strlen(sTmp));
					//g_bPenReportON = FALSE;
				}
			}
			break;

        case WM_COMMAND:
			MSGCommandHandler(hDlg, wParam, lParam);
            break;

		default:
			break;
   } // end switch message

   return FALSE;
} // end MainDlgProc

/////////////////////////////////////////////////////////////////////////
//
//
void DebugLog(char* sDbgMessage, int strLen)
{	
    INT               iIndex;

	iIndex = (INT) SendMessage(hGOutWinCtrl, LB_ADDSTRING, 0, (LPARAM) sDbgMessage);
	SendMessage(hGOutWinCtrl, LB_SETCURSEL, iIndex, 0);
	
}
/////////////////////////////////////////////////////////////////////////
//
//
void DebugShow(char* sDbgMessage, int strLen)
{	
    INT               iIndex;

	if(hGOutWinCtrl) {
		iIndex = (INT) SendMessage(hGOutWinCtrl, LB_ADDSTRING, 0, (LPARAM) sDbgMessage);
		SendMessage(hGOutWinCtrl, LB_SETCURSEL, iIndex, 0);
	}	
}

/////////////////////////////////////////////////////////////////////////
//
//
void DebugShow(char* sDbgMessage)
{	
    INT               iIndex;

	if(hGOutWinCtrl != NULL) {
		iIndex = (INT) SendMessage(hGOutWinCtrl, LB_ADDSTRING, 0, (LPARAM) sDbgMessage);
		SendMessage(hGOutWinCtrl, LB_SETCURSEL, iIndex, 0);
	}
}

/////////////////////////////////////////////////////////////////////////
//
//
void ResetShow()
{	
    SendMessage(hGOutWinCtrl, LB_RESETCONTENT, 0, (LPARAM) 0);
}

/////////////////////////////////////////////////////////////////////////
//
//
void MSGCommandHandler(HWND hDlg, WPARAM wParam, LPARAM lParam)
{
	HWND	hCheckBox = NULL;
	int iFinger, iPen;
	switch(LOWORD(wParam))
	{                                          
	case IDC_ABOUT:

		MessageBox(hDlg, _T("Touch Panel demo Application. ELAN Corp \nCopyright (C) 2016"),
						_T("About TpCtrl"), MB_ICONINFORMATION);
		break;

	case IDC_APPLY:

		iFinger = IsDlgButtonChecked(hDlg, IDC_CHKBOX_FINGER_REPORT); 

		iPen = IsDlgButtonChecked(hDlg, IDC_CHKBOX_PEN_REPORT); 

		if(iFinger == 0) {
			g_bFingerCtrlEnable = FALSE;
		} else {
			g_bFingerCtrlEnable = TRUE;
		}

		if(iPen == 0) {
			g_bPenCtrlEnable = FALSE;
		} else {
			g_bPenCtrlEnable = TRUE;
		}

		//::SendMessage(hDlg, WM_SETTINGCHANGE, NULL, (LPARAM)"Environment");
		break;

	case IDOK:
    case IDCANCEL:		
		EndDialog(hDlg,0);
		
		break;

	} //end switch wParam//
}

int Execution( EXE_CMD_TYPE cmdType )
{
	int nRet = TP_SUCCESS;
	int i = 0;
	unsigned int nVID = 0;
	unsigned int nPID = 0;
	unsigned int nFWID = 0;
	unsigned int nFWVer = 0;

	int nRXTrace = 0; 
	int nTXTrace = 0; 


	nRet = ELANAPI::Connect(0x04F3, 0x00);

	if ( nRet != TP_SUCCESS ) {
		printf( ELANAPI::GetErrMessage() );
		return 0;
	}

	switch ( cmdType ) {
	case EXE_DISABLE_TP:
		nRet = ELANAPI::DisableTPReport(true);
		if ( nRet != TP_SUCCESS ) {
			goto EXIT_EXECUTION;
		}
		break;

	case EXE_ENABLE_TP:
		nRet = ELANAPI::DisableTPReport(false);
		if ( nRet != TP_SUCCESS ) {
			goto EXIT_EXECUTION;
		}
		break;
	case EXE_DISABLE_PEN:
		nRet = ELANAPI::DisableActivePen(true);
		if ( nRet != TP_SUCCESS ) {
			goto EXIT_EXECUTION;
		}
		break;
	case EXE_ENABLE_PEN:
		nRet = ELANAPI::DisableActivePen(false);
		if ( nRet != TP_SUCCESS ) {
			goto EXIT_EXECUTION;
		}
		break;
	default:
		break;
	}

EXIT_EXECUTION: 
		nRet = ELANAPI::Disconnect();
		if ( nRet != TP_SUCCESS ) {
			printf( ELANAPI::GetErrMessage() );
		}

		if ( nRet != TP_SUCCESS ) {
			printf( "\r\nDevice error : %s", ELANAPI::GetErrMessage() );
		}
		
		return nRet;
}