#ifndef _INCLUDE_LIB_ADDRTABLE_PARAM_H_1FFC773F_54D7_4D0A_A3AE_841902AE223B
#define _INCLUDE_LIB_ADDRTABLE_PARAM_H_1FFC773F_54D7_4D0A_A3AE_841902AE223B

#include <Windows.h>
#include <string>
#include <vector>
#include <stdexcept>
#include <iostream>
#include <conio.h>	

using namespace std;

#ifdef ELANTPDLL_EXPORTS
#define ELANTPDLL_API extern "C" __declspec(dllexport) 
#else
#define ELANTPDLL_API extern "C" __declspec(dllimport)
#endif


namespace ELANAPI {

	// Misc Function: Transfer Unicode File to Ansi File
ELANTPDLL_API int __stdcall UnicodeFileToAnsiFile(const char *cUnicodeInIFileName, const char *cAnsiIniFileName);

	// This function for Loading ini file
ELANTPDLL_API bool __stdcall LoadFromInIFile(const char *sInIFileName);

	// Export ekt format data (when LoadFromInIFile succeeded)
ELANTPDLL_API void __stdcall InIExportEktFile(const char* sEktFileName);

	// Export ekt format data with last page(when LoadFromInIFile succeeded)
ELANTPDLL_API void __stdcall InIExportEktFileWithLastPage(const char* sEktFileName, unsigned char *pucLastPage);

}

#endif