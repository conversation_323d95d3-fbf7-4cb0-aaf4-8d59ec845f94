#ifndef _LIB_TOUCH_ASUS_H_INCLUDE_948228FV_85JI_2964_IE94_058276KL4A52
#define _LIB_TOUCH_ASUS_H_INCLUDE_948228FV_85JI_2964_IE94_058276KL4A52

//////////////////////////////////////////////////////////////
// Error code definition
//////////////////////////////////////////////////////////////

//** Function execution is success. **/
#ifndef TP_SUCCESS
#define TP_SUCCESS					0x0000	
#endif
/** This function is not support. **/
#ifndef TP_ERR_COMMAND_NOT_SUPPORT
#define TP_ERR_COMMAND_NOT_SUPPORT  0x0001
#endif
/** The touch ic may be occupied by other application **/
#ifndef TP_ERR_DEVICE_BUSY
#define TP_ERR_DEVICE_BUSY			0x0002
#endif
/** For asynchorous call, the execution is not finish yet. Waitting for complete **/
#ifndef TP_ERR_IO_PENDING
#define TP_ERR_IO_PENDING			0x0003 
#endif
/** Connect Elan Bridge and not get hello packet **/
#ifndef TP_ERR_CONNECT_NO_HELLO_PACKEY
#define TP_ERR_CONNECT_NO_HELLO_PACKEY		0x1002
#endif
/** Did not find any support device connectted to PC. Please check connectoin. **/
#ifndef TP_ERR_NOT_FOUND_DEVICE
#define TP_ERR_NOT_FOUND_DEVICE				0x1004
#endif
/** Test Mode Error Code **/
#ifndef TP_TESTMODE_GET_RAWDATA_FAIL
#define TP_TESTMODE_GET_RAWDATA_FAIL		0x3001			
#endif 
/** Error information check, use GetErrMsg to get the error message. **/
#ifndef TP_ERR_CHK_MSG
#define TP_ERR_CHK_MSG						0xFFFF
#endif 

//////////////////////////////////////////////////////////////
// Constant
//////////////////////////////////////////////////////////////
#ifndef MAX_TOUCH_REPORT_NUM
#define MAX_TOUCH_REPORT_NUM	100		// The maximum number of storing finger report
#endif
#ifndef MAX_TOUCH_REPORT_LEN
#define MAX_TOUCH_REPORT_LEN	116		// The maximum number of each HID report 
#endif

#pragma once
#include <Windows.h>
#include <string>
#include <vector>
#include <stdexcept>

#ifdef ELANTPDLL_EXPORTS
#define ELANTPDLL_API extern "C" __declspec(dllexport)
#else
#define ELANTPDLL_API extern "C" __declspec(dllimport)
#endif

namespace ELANAPI {

/******************************************************************
* Elan TP Library												  *
*******************************************************************
* Ver 0.0.1														  *							  
*******************************************************************/
////////////////////////////////////////////////////////
// Debug and Error message functions
////////////////////////////////////////////////////////

/*!
	When API function call get error code "TP_ERR_CHK_MSG". Use this function to get more error information.
	/Return the string describing the error condiction.
    ** Notice 01 ** 
    Most function error return value could be TP_ERR_CHK_MSG. 
    Use GetErrMsg() to get more information about the error.
*/
ELANTPDLL_API char* __stdcall GetErrMsg(void);

////////////////////////////////////////////////////////
//Declare functions : HID basic funcitons
////////////////////////////////////////////////////////

/*!
	Connect function will do the initialization and retrieve device information by the specified VID andPID. 
	@param[in] nVID Vendor ID. This value should be 0x04F3 always.
	@param[in] nPID Product ID. Specify the product ID to identify the target device. Use 0x00 will find 1st device which has VID = 0x04F3. 
	/Return	Success : TP_SUCCESS. Failure : (Notice 01)
*/
ELANTPDLL_API int __stdcall Connect(int nVID, int nPID);

/*!
	Close the connection wiht HID Device
	/Return	Success : TP_SUCCESS. Failure : (notice 01)
*/
ELANTPDLL_API int __stdcall Disconnect();

////////////////////////////////////////////////////////
// Send command 
////////////////////////////////////////////////////////

/*!
	Connect function will do the initialization and retrieve device information by the specified VID andPID. 
	@param[in] pszCommandBuf The command buffer for sending to device
	@param[in] nCommandLen The size of command buffer length in byte count.		
	/Return	Success : TP_SUCCESS. Failure : (Notice 01)
*/
ELANTPDLL_API int __stdcall SendDevCommand(unsigned char *pszCommandBuf, int nCommandLen,  int nTimeoutMS = -1, int nDevIdx = 0);

/*!
	Connect function will do the initialization and retrieve device information by the specified VID andPID. 
	@param[out] pszDataBuf The buffer for receving data.
	@param[in] nDataLen The data buffer length.
	/Return	Success : TP_SUCCESS. Failure : (Notice 01)
*/
ELANTPDLL_API int __stdcall ReadDevData(unsigned char *pszDataBuf,int nDataLen, int nTimeoutMS = -1, int nDevIdx = 0);
}
#endif // _LIB_TOUCH_ASUS_H_INCLUDE_948228FV_85JI_2964_IE94_058276KL4A52