#ifndef _INCLUDE_LIB_CSV_PARAM_H_9Y3568_9BA7BBC3_9836_682C
#define _INCLUDE_LIB_CSV_PARAM_H_9Y3568_9BA7BBC3_9836_682C

#include <Windows.h>
#include <string>
#include <vector>
#include <stdexcept>
#include <iostream>
#include <conio.h>	

using namespace std;

#ifdef ELANTPDLL_EXPORTS
#define ELANTPDLL_API extern "C" __declspec(dllexport) 
#else
#define ELANTPDLL_API extern "C" __declspec(dllimport)
#endif

////////////////////////////////////////////////////////////
//Declare the constant value
////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////
// Connect 
////////////////////////////////////////////////////////////


#define SIZE_1K					1024
#define MAX_MSG_LENGTH			SIZE_1K
#define MAX_ROM_SIZE			(32*SIZE_1K) //32768
#define MAX_ONEPAGE_LENGTH		132
#define MAX_PAGE_WORD_SIZE		32	//0x20
#define MAX_PAGE_BYTE_SIZE		(MAX_PAGE_WORD_SIZE*2)  //0x40 = 64
#define MAX_USAGE_PAGE_LENGTH	(MAX_ROM_SIZE/MAX_PAGE_BYTE_SIZE)	//512
//Modified by J2 20170123 
//Extend the max size form 128 to 256 
#define MAX_PARAM_VAR_LENGTH	256
//~Modified by J2


#pragma pack(push,1)
typedef struct _PARAM_VAR {
	char	cIndex[MAX_PARAM_VAR_LENGTH];
	char	cAddress[MAX_PARAM_VAR_LENGTH];
	char	cClassification[MAX_PARAM_VAR_LENGTH];
	char	cName[MAX_PARAM_VAR_LENGTH];
	char	cDefaultValue[MAX_PARAM_VAR_LENGTH];
	char	cRange[MAX_PARAM_VAR_LENGTH];// Max ~ Min
	char	cAssociatedExpression[MAX_PARAM_VAR_LENGTH];
	char	cFunctionDescriptor[MAX_PARAM_VAR_LENGTH];
	char	cPublic[MAX_PARAM_VAR_LENGTH];
	char	cSmallValueSideEffect[MAX_PARAM_VAR_LENGTH];
	char	cLargeValueSideEffect[MAX_PARAM_VAR_LENGTH];
	char	cTag1[MAX_PARAM_VAR_LENGTH];
	char	cTag2[MAX_PARAM_VAR_LENGTH];
	char	cTag3[MAX_PARAM_VAR_LENGTH];
} *PPARAM_VAR, PARAM_VAR;

#define MAX_PARAM_VAR_COUNT		sizeof(PARAM_VAR)/MAX_PARAM_VAR_LENGTH //14

typedef struct _CSV_PARAM_ {
	
	union {
		char			cParam[MAX_PARAM_VAR_COUNT][MAX_PARAM_VAR_LENGTH];
		PARAM_VAR		vParamVar;
	};

	unsigned int uiIndex;
	unsigned int uiAddress;

	unsigned int uiValue;

	unsigned int uiMax;
	unsigned int uiMin;

	bool	bExprCheck;					// For C# dll import issue, the struct length
	unsigned char ucReservedByte[3];	// should be the multiple size of 4 byts.
} *PCSV_PARAM, CSV_PARAM;
#pragma pack(pop)

namespace ELANAPI {

// This function for Loading csv file and return TitleCount&ParamCount 
ELANTPDLL_API int __stdcall LoadFromCsvFile(char *sCsvFileName, int *p_iTitleCount, int *p_iParamCount);

	// Get SolutionID, Interface and ParameterValue from CSV file
ELANTPDLL_API int __stdcall GetCsvFileInfo(int *p_iSolutionID, int *p_iInterface, int *p_iParameterValue);

	// Get title array at this csv file
ELANTPDLL_API int __stdcall GetCsvTitle(char* sTitle, int iLength, int index);

	// Get param array at this csv file
ELANTPDLL_API int __stdcall GetCsvParam(PCSV_PARAM p_eParam, int iParamCount);

	// Set the data struct of specified address.(Need to set Param Count)
	// Inside this function, check associated expression
ELANTPDLL_API int __stdcall SetCsvParam(PCSV_PARAM p_eParam, int iParamCount);

	// Export ekt format data (when SetCsvParam succeeded)
ELANTPDLL_API int __stdcall CsvExportEktFile(char* sEktFileName);

	// Export ekt format data with last page(when SetCsvParam succeeded)
ELANTPDLL_API int __stdcall CsvExportEktFileWithLastPage(char* sEktFileName, unsigned char *pucLastPage);

}
#endif