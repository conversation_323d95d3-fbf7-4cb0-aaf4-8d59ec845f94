﻿#include <winsock2.h>
#include <windows.h>
#include <ws2tcpip.h>
#include <stdio.h>
#include <setupapi.h>
#include <initguid.h>
#include <devguid.h>
#include <devpkey.h>
#include <iostream>
#include <setupapi.h>
#include <hidsdi.h>
#include <Hidclass.h>
#include <iostream>
//#include <Bthdef.h>
#include <bthledef.h>
#include <bluetoothleapis.h>
#include <bluetoothapis.h>
#include "scanLED.h"



#pragma comment(lib, "Bthprops.lib")
// Need to link with Ws2_32.lib
#pragma comment(lib, "ws2_32.lib")

const CHAR* UUIDToString(unsigned int uuid)
{
    switch (uuid) {
    case 0x2A00:
        return "2A00 - Device Name";
    case 0x2A01:
        return "2A01 - Appearance";
    case 0x2A02:
        return "2A02 - Peripheral Privacy Flag";
    case 0x2A03:
        return "2A03 - Reconnection Address";
    case 0x2A04:
        return "2A04 - Peripheral Preferred Connection Parameters";
    case 0x2A05:
        return "2A05 - Service Changed";
    case 0x2A09:
        return "2A09 - Alert Category ID";
    case 0x2A19:
        return "2A19 - Battery Level";
    case 0x2A20:
        return "2A20 - Navigation";
    case 0x2A26:
        return "2A26 - Firmware Revision String";
    case 0x2A25:
        return "2A25 - Serial Number String";

    case 0x2A50:
        return "2A50 - Device ID";
    case 0x2A41:
        return "2A41 - Alert Notification Control Point";
    case 0x2A4E:
        return "2A4E - Protocol Mode";
    case 0x2A4D:
        return "2A4D - Report";
    case 0x2A4C:
        return "2A4C - Report Map";
    case 0x2A4B:
        return "2A4B - HID Information";
    case 0x2A4A:
        return "2A4A - HID Control Point";
    case 0x2A33:
        return "2A33 - Alert Category ID Bit Mask";
        // Add more cases as needed

    default:
        return "Unknown UUID";
    }
}

const CHAR* HResultToString(HRESULT hr)
{
    switch (hr) {
    case S_OK:
        return ("Success");
    case E_FAIL:
        return ("Unspecified failure");
    case E_OUTOFMEMORY:
        return ("Out of memory");
    case HRESULT_FROM_WIN32(ERROR_MORE_DATA):
        return ("ERROR_MORE_DATA");
    case HRESULT_FROM_WIN32(ERROR_ACCESS_DENIED):
        return ("ERROR_ACCESS_DENIED");
    case HRESULT_FROM_WIN32(ERROR_INVALID_PARAMETER):
        return ("ERROR_INVALID_PARAMETER");     // Both DescriptorValueand DescriptorValueSizeRequired are 0.
    case HRESULT_FROM_WIN32(ERROR_INVALID_USER_BUFFER):
        return ("ERROR_INVALID_USER_BUFFER");   // A buffer is specified, but the buffer count size is smaller than what is required, in bytes.
    case HRESULT_FROM_WIN32(ERROR_INVALID_FUNCTION):
        return ("ERROR_INVALID_FUNCTION");      // A descriptor value was specified to be retrieved from the cache, but the descriptor value is not present in the cache.
    case HRESULT_FROM_WIN32(ERROR_BAD_COMMAND):
        return ("ERROR_BAD_COMMAND");           // The current data in the cache appears to be inconsistent, and is leading to internal errors.
    case HRESULT_FROM_WIN32(ERROR_BAD_NET_RESP):
        return ("ERROR_BAD_NET_RESP");          // The target server did not provide an appropriate network response.
    case HRESULT_FROM_WIN32(ERROR_SEM_TIMEOUT):
        return ("ERROR_SEM_TIMEOUT");
    case HRESULT_FROM_WIN32(ERROR_NO_SYSTEM_RESOURCES): // The operation ran out of memory.
        return ("ERROR_NO_SYSTEM_RESOURCES");
    case HRESULT_FROM_WIN32(E_BLUETOOTH_ATT_INVALID_HANDLE):    // The attribute handle given was not valid on this server.
        return ("E_BLUETOOTH_ATT_INVALID_HANDLE");  
    case HRESULT_FROM_WIN32(ERROR_NOT_FOUND):
        return ("ERROR_NOT_FOUND");
    default:
        return ("Unknown UUID");
    }
}

bool IsBthLeUuidEqual(const BTH_LE_UUID& uuid1, const BTH_LE_UUID& uuid2) {
    if (uuid1.IsShortUuid && uuid2.IsShortUuid) {
        // 两个都是短 UUID
        return uuid1.Value.ShortUuid == uuid2.Value.ShortUuid;
    }
    else if (!uuid1.IsShortUuid && !uuid2.IsShortUuid) {
        // 两个都是长 UUID
        return IsEqualGUID(uuid1.Value.LongUuid, uuid2.Value.LongUuid);
    }
    else {
        // 一个是短 UUID，一个是长 UUID
        // 这种情况需要将短 UUID 转换成长 UUID 后进行比较
        const BTH_LE_UUID* shortUuid = uuid1.IsShortUuid ? &uuid1 : &uuid2;
        const BTH_LE_UUID* longUuid = uuid1.IsShortUuid ? &uuid2 : &uuid1;

        GUID convertedShortUuid = { 0 };
        convertedShortUuid.Data1 = shortUuid->Value.ShortUuid;
        convertedShortUuid.Data2 = 0;
        convertedShortUuid.Data3 = 0x1000;
        convertedShortUuid.Data4[0] = 0x80;
        convertedShortUuid.Data4[1] = 0x00;
        convertedShortUuid.Data4[2] = 0x00;
        convertedShortUuid.Data4[3] = 0x80;
        convertedShortUuid.Data4[4] = 0x5F;
        convertedShortUuid.Data4[5] = 0x9B;
        convertedShortUuid.Data4[6] = 0x34;
        convertedShortUuid.Data4[7] = 0xFB;

        return IsEqualGUID(convertedShortUuid, longUuid->Value.LongUuid);
    }
}


class BluetoothGattReader {
public:
    BluetoothGattReader(HANDLE deviceHandle) : hDevice(deviceHandle) {}

    bool ReadCharacteristic(const BTH_LE_UUID& characteristicUuid, BYTE*& buffer, ULONG& bufferSize) {
        if (!hDevice) {
            std::cerr << "Invalid device handle." << std::endl;
            return false;
        }

        return ReadCharacteristicInternal(characteristicUuid, buffer, bufferSize);
    }

    bool ReadDescriptor(const BTH_LE_UUID& descriptorUuid, BYTE*& buffer, ULONG& bufferSize) {
        if (!hDevice) {
            std::cerr << "Invalid device handle." << std::endl;
            return false;
        }

        return ReadDescriptorInternal(descriptorUuid, buffer, bufferSize);
    }

private:
    HANDLE hDevice;

    bool ReadDescriptorInternal(const BTH_LE_UUID& descriptorUuid, BYTE*& buffer, ULONG& bufferSize) {

        char tmp[512];

        USHORT serviceBufferCount;
        HRESULT hr = BluetoothGATTGetServices(
            hDevice,
            0,
            NULL,
            &serviceBufferCount,
            BLUETOOTH_GATT_FLAG_NONE);

        if (HRESULT_FROM_WIN32(ERROR_MORE_DATA) != hr) {
            std::cerr << "Error retrieving service count, HRESULT: " << HResultToString(hr) << std::endl;
            return false;
        }

        BTH_LE_GATT_SERVICE* pServices = new BTH_LE_GATT_SERVICE[serviceBufferCount];
        hr = BluetoothGATTGetServices(
            hDevice,
            serviceBufferCount,
            pServices,
            &serviceBufferCount,
            BLUETOOTH_GATT_FLAG_NONE);

        if (FAILED(hr)) {
            std::cerr << "Error retrieving services, HRESULT: " << HResultToString(hr) << std::endl;
            delete[] pServices;
            return false;
        }

        sprintf_s(tmp, "Total %d services.", serviceBufferCount);
        std::cout << tmp;

        bool success = false;

        for (USHORT i = 0; i < serviceBufferCount && !success; i++)             
        {
            USHORT charBufferCount;
            hr = BluetoothGATTGetCharacteristics(
                hDevice,
                &pServices[i],
                0,
                NULL,
                &charBufferCount,
                BLUETOOTH_GATT_FLAG_NONE);

            if (HRESULT_FROM_WIN32(ERROR_MORE_DATA) != hr) {
                std::cerr << "Error retrieving characteristic count, HRESULT: " << HResultToString(hr) << std::endl;
                continue;
            }

            BTH_LE_GATT_CHARACTERISTIC* pCharacteristics = new BTH_LE_GATT_CHARACTERISTIC[charBufferCount];
            hr = BluetoothGATTGetCharacteristics(
                hDevice,
                &pServices[i],
                charBufferCount,
                pCharacteristics,
                &charBufferCount,
                BLUETOOTH_GATT_FLAG_NONE);

            if (FAILED(hr)) {
                std::cerr << "Error retrieving characteristics, HRESULT: " << HResultToString(hr) << std::endl;
                delete[] pCharacteristics;
                continue;
            }

            for (USHORT j = 0; j < charBufferCount && !success; j++) {
                USHORT descBufferCount;
                hr = BluetoothGATTGetDescriptors(
                    hDevice,
                    &pCharacteristics[j],
                    0,
                    NULL,
                    &descBufferCount,
                    BLUETOOTH_GATT_FLAG_NONE);

                if (FAILED(hr)) {
                    std::cerr << "Error retrieving descriptor count, HRESULT: " << HResultToString(hr) << std::endl;
                    continue;
                }

                BTH_LE_GATT_DESCRIPTOR* pDescriptors = new BTH_LE_GATT_DESCRIPTOR[descBufferCount];
                hr = BluetoothGATTGetDescriptors(
                    hDevice,
                    &pCharacteristics[j],
                    descBufferCount,
                    pDescriptors,
                    &descBufferCount,
                    BLUETOOTH_GATT_FLAG_NONE);

                if (FAILED(hr)) {
                    std::cerr << "Error retrieving descriptors, HRESULT: " << hr << std::endl;
                    delete[] pDescriptors;
                    continue;
                }

                for (USHORT k = 0; k < descBufferCount && !success; k++) {
                    if (IsBthLeUuidEqual(pDescriptors[k].DescriptorUuid, descriptorUuid)) {
                        USHORT descValueDataSize;
                        hr = BluetoothGATTGetDescriptorValue(
                            hDevice,
                            &pDescriptors[k],
                            0,
                            NULL,
                            &descValueDataSize,
                            BLUETOOTH_GATT_FLAG_NONE);

                        if (FAILED(hr)) {
                            std::cerr << "Error retrieving descriptor value size, HRESULT: " << hr << std::endl;
                            continue;
                        }

                        BTH_LE_GATT_DESCRIPTOR_VALUE* pDescValue = new BTH_LE_GATT_DESCRIPTOR_VALUE[descValueDataSize];
                        hr = BluetoothGATTGetDescriptorValue(
                            hDevice,
                            &pDescriptors[k],
                            descValueDataSize,
                            pDescValue,
                            NULL,
                            BLUETOOTH_GATT_FLAG_NONE);

                        if (SUCCEEDED(hr)) {
                            buffer = new BYTE[descValueDataSize];
                            memcpy(buffer, pDescValue->Data, descValueDataSize);
                            bufferSize = descValueDataSize;
                            success = true;
                        }
                        else {
                            std::cerr << "Error retrieving descriptor value, HRESULT: " << hr << std::endl;
                        }

                        delete[] pDescValue;
                        break;
                    }
                }
                delete[] pDescriptors;
            }
            delete[] pCharacteristics;
        }
        delete[] pServices;

        return success;
    }

    bool ReadCharacteristicInternal(const BTH_LE_UUID& characteristicUuid, BYTE*& buffer, ULONG& bufferSize) {


        char tmp[512];

        USHORT serviceBufferCount;
        USHORT numDescriptors;

        BTH_LE_GATT_CHARACTERISTIC_VALUE* pCharValue = NULL;
        PBTH_LE_GATT_DESCRIPTOR pDescriptorBuffer;

        HRESULT hr = BluetoothGATTGetServices(
            hDevice,
            0,
            NULL,
            &serviceBufferCount,
            BLUETOOTH_GATT_FLAG_RETURN_ALL);

        if (HRESULT_FROM_WIN32(ERROR_MORE_DATA) != hr) {
            std::cerr << "Error retrieving service count, HRESULT: " << HResultToString(hr) << std::endl;
            return false;
        }

        BTH_LE_GATT_SERVICE* pServices = new BTH_LE_GATT_SERVICE[serviceBufferCount];
        hr = BluetoothGATTGetServices(
            hDevice,
            serviceBufferCount,
            pServices,
            &serviceBufferCount,
            BLUETOOTH_GATT_FLAG_RETURN_ALL);

        if (FAILED(hr)) {
            std::cerr << "Error retrieving services, HRESULT: " << HResultToString(hr) << std::endl;
            delete[] pServices;
            return false;
        }

        sprintf_s(tmp, "< Total %d services. > \r\n", serviceBufferCount);
        std::cout << tmp;

        bool success = false;

        for (USHORT i = 0; i < serviceBufferCount && !success; i++) {
            USHORT charBufferCount;
            hr = BluetoothGATTGetCharacteristics(
                hDevice,
                &pServices[i],
                0,
                NULL,
                &charBufferCount,
                BLUETOOTH_GATT_FLAG_NONE);

            if (HRESULT_FROM_WIN32(ERROR_MORE_DATA) != hr) {
                std::cerr << "Error retrieving characteristic count, HRESULT: " << HResultToString(hr) << std::endl;
                continue;
            }


            /// <summary>
            /// ////////////////
            /// </summary>
            /// <param name="characteristicUuid"></param>
            /// <param name="buffer"></param>
            /// <param name="bufferSize"></param>
            /// <returns></returns>
            BTH_LE_GATT_CHARACTERISTIC* pCharacteristics = new BTH_LE_GATT_CHARACTERISTIC[charBufferCount];
            hr = BluetoothGATTGetCharacteristics(
                hDevice,
                &pServices[i],
                charBufferCount,
                pCharacteristics,
                &charBufferCount,
                BLUETOOTH_GATT_FLAG_NONE);

            if (FAILED(hr)) {
                std::cerr << "Error retrieving characteristics, HRESULT: " << HResultToString(hr) << std::endl;
                delete[] pCharacteristics;
                continue;
            }


            sprintf_s(tmp, "< Total %d Characteristic. > \r\n", charBufferCount);
            std::cout << tmp;

            BTH_LE_GATT_CHARACTERISTIC* curCharacteristic = NULL;

            for (USHORT j = 0; j < charBufferCount && !success; j++) {

                curCharacteristic = &pCharacteristics[j];

                sprintf_s(tmp, "Current Uuid = %04X ", curCharacteristic->CharacteristicUuid.Value.ShortUuid);
                std::cout << tmp;

                std::cout << UUIDToString(curCharacteristic->CharacteristicUuid.Value.ShortUuid);

                if (curCharacteristic->IsReadable == TRUE){
                    std::cout << " Readable\r\n";
                }
                else{
                    std::cout << " UnReadable\r\n";
                }

                USHORT charValueDataSize = 0;
                USHORT descriptorBufferSize = 0;
                

                //if (IsBthLeUuidEqual(pCharacteristics[j].CharacteristicUuid, characteristicUuid)) {
                    //USHORT charValueDataSize = 0;
                    hr = BluetoothGATTGetCharacteristicValue(
                        hDevice,                        // _In_ HANDLE hDevice,                                                                     
                        curCharacteristic,              // _In_ PBTH_LE_GATT_CHARACTERISTIC Characteristic,
                        0,                              // _In_ ULONG CharacteristicValueDataSize,
                        NULL,                           // _Out_opt_ PBTH_LE_GATT_CHARACTERISTIC_VALUE CharacteristicValue,
                        &charValueDataSize,             // _Out_opt_ USHORT * CharacteristicValueSizeRequired,
                        BLUETOOTH_GATT_FLAG_FORCE_READ_FROM_DEVICE);        // _In_ ULONG Flags

                    if (HRESULT_FROM_WIN32(ERROR_MORE_DATA) == hr) {

                    }
                    else {
                        std::cerr << "Error retrieving Characteristic Value, HRESULT: " << HResultToString(hr) << std::endl;
                        goto GetDescriptors;
                    }


                    pCharValue = new BTH_LE_GATT_CHARACTERISTIC_VALUE[charValueDataSize];
                    hr = BluetoothGATTGetCharacteristicValue(
                        hDevice,
                        curCharacteristic,
                        charValueDataSize,
                        pCharValue,
                        NULL,
                        BLUETOOTH_GATT_FLAG_FORCE_READ_FROM_DEVICE);

                    if (SUCCEEDED(hr)) {
                        buffer = new BYTE[charValueDataSize];
                        memcpy(buffer, pCharValue->Data, charValueDataSize);
                        bufferSize = charValueDataSize;
                        success = true;
                    }
                    else {
                        std::cerr << "Error retrieving characteristic value, HRESULT: " << HResultToString(hr) << std::endl;
                    }
                    delete[] pCharValue;
                //}

        ////////////////////////////////////////////////////////////////////////////
        // Determine Descriptor Buffer Size
        ////////////////////////////////////////////////////////////////////////////
                GetDescriptors:                

                    hr = BluetoothGATTGetDescriptors(
                        hDevice,                                        // _In_ HANDLE hDevice,
                        curCharacteristic,                              // _In_ PBTH_LE_GATT_CHARACTERISTIC Characteristic,
                        0,                                              // _In_ USHORT DescriptorsBufferCount,
                        NULL,                                           //  [out, optional] PBTH_LE_GATT_DESCRIPTOR     DescriptorsBuffer,
                        &descriptorBufferSize,                          // _Out_ USHORT * DescriptorsBufferActual,
                        BLUETOOTH_GATT_FLAG_NONE);     // _In_ ULONG Flags

                    if (HRESULT_FROM_WIN32(ERROR_MORE_DATA) != hr) {
                        std::cerr << "Error retrieving Descriptors count, HRESULT: " << HResultToString(hr) << std::endl;
                        continue;
                    }

                    if (descriptorBufferSize > 0) {
                        pDescriptorBuffer = (PBTH_LE_GATT_DESCRIPTOR)
                            malloc(descriptorBufferSize
                                * sizeof(BTH_LE_GATT_DESCRIPTOR));

                        if (NULL == pDescriptorBuffer) {
                            std::cerr <<  "pDescriptorBuffer out of memory\r\n";
                            continue;
                        }
                        else {
                            RtlZeroMemory(pDescriptorBuffer, descriptorBufferSize);
                        }

                        ////////////////////////////////////////////////////////////////////////////
                        // Retrieve Descriptors
                        ////////////////////////////////////////////////////////////////////////////

                        hr = BluetoothGATTGetDescriptors(
                            hDevice,
                            curCharacteristic,
                            descriptorBufferSize,
                            pDescriptorBuffer,
                            &numDescriptors,
                            BLUETOOTH_GATT_FLAG_NONE);

                        if (S_OK != hr) {
                            continue;
                        }

                        if (numDescriptors != descriptorBufferSize) {
                            std::cerr << "buffer size and buffer size actual size mismatch\r\n";
                            continue;
                        }

                        BTH_LE_GATT_DESCRIPTOR_VALUE descriptorValue;
                        RtlZeroMemory(&descriptorValue, sizeof(BTH_LE_GATT_DESCRIPTOR_VALUE));

                        USHORT descriptorValueDataSize;
                        HRESULT hr = BluetoothGATTGetDescriptorValue(
                            hDevice,
                            pDescriptorBuffer,              // The descriptor you got from BluetoothGATTGetDescriptors
                            0,            // The size of the buffer, set to 0 initially to get the size required
                            NULL,         // Buffer, NULL for the first call
                            &descriptorValueDataSize,
                            BLUETOOTH_GATT_FLAG_NONE);

                        if (HRESULT_FROM_WIN32(ERROR_MORE_DATA) == hr) {
                            // Allocate memory for the descriptor value based on the size returned
                            PBTH_LE_GATT_DESCRIPTOR_VALUE pDescriptorValueBuffer =
                                (PBTH_LE_GATT_DESCRIPTOR_VALUE)malloc(descriptorValueDataSize);

                            if (pDescriptorValueBuffer != NULL) {
                                // Now call the function again with the actual buffer
                                hr = BluetoothGATTGetDescriptorValue(
                                    hDevice,
                                    pDescriptorBuffer,
                                    descriptorValueDataSize,
                                    pDescriptorValueBuffer,
                                    &descriptorValueDataSize,
                                    BLUETOOTH_GATT_FLAG_NONE);

                                if (SUCCEEDED(hr)) {
                                    // Successfully read the descriptor value
                                    // Process pDescriptorValueBuffer here...
                                    break;

                                }
                                free(pDescriptorValueBuffer); // Remember to free the allocated memory
                            }
                        }
                    }
            }
            delete[] pCharacteristics;
        }



        delete[] pServices;

        return success;
    }


    //bool IsBthLeUuidEqual(const BTH_LE_UUID& uuid1, const BTH_LE_UUID& uuid2) {
        // Implement the UUID comparison logic here
        // ...
    //}
};


HRESULT ReadGattCharacteristic(HANDLE hDevice, PBTH_LE_GATT_SERVICE pService, BTH_LE_UUID characteristicUuid, PBYTE buffer, ULONG buffer_size) {
    USHORT characteristicBufferCount;
    PBTH_LE_GATT_CHARACTERISTIC pCharacteristics = nullptr;
    HRESULT hr = S_OK;

    // 获取特性数量
    hr = BluetoothGATTGetCharacteristics(hDevice, pService, 0, NULL, &characteristicBufferCount, BLUETOOTH_GATT_FLAG_RETURN_ALL);
    if (HRESULT_FROM_WIN32(ERROR_MORE_DATA) != hr) {
        std::cerr << "Error retrieving characteristic count, HRESULT: " << hr << std::endl;
        return hr;
    }

    if (characteristicBufferCount > 0) {
        pCharacteristics = (PBTH_LE_GATT_CHARACTERISTIC)malloc(sizeof(BTH_LE_GATT_CHARACTERISTIC) * characteristicBufferCount);

        // 获取特性
        hr = BluetoothGATTGetCharacteristics(hDevice, pService, characteristicBufferCount, pCharacteristics, &characteristicBufferCount, BLUETOOTH_GATT_FLAG_RETURN_ALL);
        if (S_OK != hr) {
            std::cerr << "Error retrieving characteristics, HRESULT: " << hr << std::endl;
            free(pCharacteristics);
            return hr;
        }
        PBTH_LE_GATT_CHARACTERISTIC_VALUE pCharValueBuffer;

        USHORT numDescriptors;
        USHORT descriptorBufferSize;
        PBTH_LE_GATT_DESCRIPTOR pDescriptorBuffer;

        // 遍历找到对应的特性
        for (USHORT i = 0; i < characteristicBufferCount; i++) {

            //std::cout <<("cuurent uuid = %04x", pCharacteristics[i].CharacteristicUuid );

            if (IsBthLeUuidEqual(pCharacteristics[i].CharacteristicUuid, characteristicUuid)) {
                PBTH_LE_GATT_CHARACTERISTIC_VALUE pCharValue = nullptr;
                USHORT charValueDataSize;

                PBTH_LE_GATT_CHARACTERISTIC currGattChar = new (BTH_LE_GATT_CHARACTERISTIC);

                hr = BluetoothGATTGetCharacteristicValue(
                    pService,
                    currGattChar,
                    0,
                    NULL,
                    &charValueDataSize,
                    BLUETOOTH_GATT_FLAG_NONE);

                if (HRESULT_FROM_WIN32(ERROR_MORE_DATA) != hr) {
                    //PrintHr("BluetoothGATTGetCharacteristicValue - Buffer Size", hr);
                    goto GetDescriptors; // Proceed to retrieving descriptors
                }

                pCharValueBuffer = (PBTH_LE_GATT_CHARACTERISTIC_VALUE)malloc(charValueDataSize);

                if (NULL == pCharValueBuffer) {
                    printf("pCharValueBuffer out of memory\r\n");
                    break;
                }
                else {
                    RtlZeroMemory(pCharValueBuffer, charValueDataSize);
                }

                ////////////////////////////////////////////////////////////////////////////
                // Retrieve the Characteristic Value
                ////////////////////////////////////////////////////////////////////////////

                hr = BluetoothGATTGetCharacteristicValue(
                    pService,
                    currGattChar,
                    (ULONG)charValueDataSize,
                    pCharValueBuffer,
                    NULL,
                    BLUETOOTH_GATT_FLAG_NONE);

                if (S_OK != hr) {
                    std::cerr << "BluetoothGATTGetCharacteristicValue - Actual Data" << hr << std::endl;
                    goto GetDescriptors; // Proceed to retrieving descriptors
                }

                //PrintCharacteristicValue(pCharValueBuffer, 2, currGattChar->CharacteristicUuid);

                // 获取特性值大小
                hr = BluetoothGATTGetCharacteristicValue(hDevice, &pCharacteristics[i], 0, NULL, (USHORT*)&charValueDataSize, BLUETOOTH_GATT_FLAG_NONE);
                if (HRESULT_FROM_WIN32(ERROR_MORE_DATA) != hr) {
                    std::cerr << "Error retrieving characteristic value size, HRESULT: " << hr << std::endl;
                    break;
                }

                pCharValue = (PBTH_LE_GATT_CHARACTERISTIC_VALUE)malloc(charValueDataSize);

                // 读取特性值
                hr = BluetoothGATTGetCharacteristicValue(hDevice, &pCharacteristics[i], charValueDataSize, pCharValue, NULL, BLUETOOTH_GATT_FLAG_NONE);
                if (S_OK != hr) {
                    std::cerr << "Error retrieving characteristic value, HRESULT: " << hr << std::endl;
                }
                else {
                    // 这里假设数据是字符串类型
                    memcpy(buffer, pCharValue->Data, min(buffer_size, pCharValue->DataSize));
                }

                free(pCharValue);
                break;

                ////////////////////////////////////////////////////////////////////////////
                // Determine Descriptor Buffer Size
                ////////////////////////////////////////////////////////////////////////////
            GetDescriptors:
                hr = BluetoothGATTGetDescriptors(
                    pService,
                    currGattChar,
                    0,
                    NULL,
                    &descriptorBufferSize,
                    BLUETOOTH_GATT_FLAG_NONE);

                if (HRESULT_FROM_WIN32(ERROR_MORE_DATA) != hr) {
                    //PrintHr("BluetoothGATTGetDescriptors - Buffer Size", hr);
                    //goto Done; // Allow continuation
                    continue;
                }

                
                if (descriptorBufferSize > 0) {
                    pDescriptorBuffer = (PBTH_LE_GATT_DESCRIPTOR)
                        malloc(descriptorBufferSize
                            * sizeof(BTH_LE_GATT_DESCRIPTOR));

                    if (NULL == pDescriptorBuffer) {
                        printf("pDescriptorBuffer out of memory\r\n");
                        //goto Done;
                        continue;
                    }
                    else {
                        RtlZeroMemory(pDescriptorBuffer, descriptorBufferSize);
                    }

                    ////////////////////////////////////////////////////////////////////////////
                    // Retrieve Descriptors
                    ////////////////////////////////////////////////////////////////////////////

                    hr = BluetoothGATTGetDescriptors(
                        pService,
                        currGattChar,
                        descriptorBufferSize,
                        pDescriptorBuffer,
                        &numDescriptors,
                        BLUETOOTH_GATT_FLAG_NONE);

                    if (S_OK != hr) {
                        //PrintHr("BluetoothGATTGetDescriptors - Actual Data", hr);
                        //goto Done;
                        continue;
                    }

                    if (numDescriptors != descriptorBufferSize) {
                        printf("buffer size and buffer size actual size mismatch\r\n");
                        //goto Done;
                        continue;
                    }
                }
            }
        }
    }

    if (pCharacteristics) {
        free(pCharacteristics);
    }

    return hr;
}

void GetDeviceInformation(HANDLE hDevice) {
    //USHORT serviceBufferCount;
    //// 獲取服務緩衝區大小
    //HRESULT hr = BluetoothGATTGetServices(hDevice, 0, NULL, &serviceBufferCount, BLUETOOTH_GATT_FLAG_NONE);
    //if (HRESULT_FROM_WIN32(ERROR_MORE_DATA) != hr) {
    //    // 處理錯誤
    //}

    //PBTH_LE_GATT_SERVICE services = (PBTH_LE_GATT_SERVICE)malloc(sizeof(BTH_LE_GATT_SERVICE) * serviceBufferCount);
    //hr = BluetoothGATTGetServices(hDevice, serviceBufferCount, services, &serviceBufferCount, BLUETOOTH_GATT_FLAG_NONE);
    //if (S_OK != hr) {
    //    // 處理錯誤
    //}

    // 定义您想要读取的特性的 UUID
    BTH_LE_UUID characteristicUuid;
    characteristicUuid.IsShortUuid = TRUE; // 如果是短 UUID 则设为 TRUE，否则设为 FALSE
    characteristicUuid.Value.ShortUuid = 0x2A29; // 用您想读取的特性的 UUID 替换这里的值

    // 创建 BluetoothGattReader 类的实例
    BluetoothGattReader reader(hDevice);

    // 创建用于接收数据的 BYTE 指针和数据大小变量
    BYTE* dataBuffer = nullptr;
    ULONG dataSize = 0;

    // 读取特性
    if (reader.ReadCharacteristic(characteristicUuid, dataBuffer, dataSize)) {
        std::cout << "Characteristic data read successfully." << std::endl;
        // 这里处理 dataBuffer，例如打印数据
        for (ULONG i = 0; i < dataSize; ++i) {
            std::cout << static_cast<int>(dataBuffer[i]) << " ";
        }
        std::cout << std::endl;

        delete[] dataBuffer; // 完成后释放分配的内存
    }
    else {
        std::cerr << "Failed to read characteristic data." << std::endl;
    }

    //free(services);
}

int GetGATTproberty(HANDLE hDevice) {
    // 假設 hDevice 是你已獲取的 BLE 裝置句柄    

    USHORT serviceBufferCount;
    // 第一次調用是為了獲取所需的緩衝區大小
    HRESULT hr = BluetoothGATTGetServices(
        hDevice,
        0,
        NULL,
        &serviceBufferCount,
        BLUETOOTH_GATT_FLAG_NONE);

    if (HRESULT_FROM_WIN32(ERROR_MORE_DATA) != hr) {
        // 處理錯誤
    }

    PBTH_LE_GATT_SERVICE services = (PBTH_LE_GATT_SERVICE)malloc(sizeof(BTH_LE_GATT_SERVICE) * serviceBufferCount);

    hr = BluetoothGATTGetServices(
        hDevice,
        serviceBufferCount,
        services,
        &serviceBufferCount,
        BLUETOOTH_GATT_FLAG_NONE);

    if (S_OK != hr) {
        // 處理錯誤
    }

    // 遍歷服務
    for (USHORT i = 0; i < serviceBufferCount; i++) {
        PBTH_LE_GATT_SERVICE service = &services[i];
        // 使用 service 進行相關操作，例如讀取特性
        // ...
    }

    // 記得釋放資源
    free(services);

    return 0;
}

int GetHIDDev() {
    // 初始化COM
    CoInitialize(NULL);

    GUID BluetoothInterfaceGUID = GUID_BLUETOOTHLE_DEVICE_INTERFACE;

    // 獲取HID類別的設備信息集合
    HDEVINFO hDevInfo = SetupDiGetClassDevs(&BluetoothInterfaceGUID, NULL, NULL, DIGCF_PRESENT | DIGCF_DEVICEINTERFACE);
    if (hDevInfo == INVALID_HANDLE_VALUE) {
        CoUninitialize();
        return 1;
    }

    SP_DEVICE_INTERFACE_DATA devInterfaceData;
    devInterfaceData.cbSize = sizeof(SP_DEVICE_INTERFACE_DATA);

    // 遍歷設備
    for (DWORD i = 0; SetupDiEnumDeviceInterfaces(hDevInfo, NULL, &BluetoothInterfaceGUID, i, &devInterfaceData); i++) {
        DWORD size = 0;

        // 獲取設備接口詳細信息的所需大小
        SetupDiGetDeviceInterfaceDetail(hDevInfo, &devInterfaceData, NULL, 0, &size, NULL);

        // 分配記憶體
        PSP_DEVICE_INTERFACE_DETAIL_DATA devInterfaceDetailData = (PSP_DEVICE_INTERFACE_DETAIL_DATA)malloc(size);
        devInterfaceDetailData->cbSize = sizeof(SP_DEVICE_INTERFACE_DETAIL_DATA);

        SP_DEVINFO_DATA devInfoData;
        devInfoData.cbSize = sizeof(SP_DEVINFO_DATA);

        // 獲取設備接口詳細信息
        if (SetupDiGetDeviceInterfaceDetail(hDevInfo, &devInterfaceData, devInterfaceDetailData, size, &size, &devInfoData)) {
            // 檢查VID和PID
            WCHAR deviceID[MAX_PATH];
            if (SetupDiGetDeviceInstanceId(hDevInfo, &devInfoData, deviceID, MAX_PATH, NULL)) {
                std::wstring deviceIDStr(deviceID);

                //if (deviceIDStr.find(L"BTHLE") != std::wstring::npos) {

                //if (deviceIDStr.find(L"VID_0204") != std::wstring::npos &&
                //    deviceIDStr.find(L"PID_0805") != std::wstring::npos) {
                    // 找到匹配的設備
                    HANDLE hDevice = CreateFile(devInterfaceDetailData->DevicePath, GENERIC_READ /* | GENERIC_WRITE*/, FILE_SHARE_READ /* | FILE_SHARE_WRITE */ , NULL, OPEN_EXISTING, 0, NULL);
                    if (hDevice != INVALID_HANDLE_VALUE) {
                        // 成功獲取句柄
                        std::wcout << L"\r\nDevice Handle: " << deviceIDStr << std::endl;
                        GetDeviceInformation(hDevice);
                        //ConnectBLEDevice(hDevice);
                        CloseHandle(hDevice);
                    }
                    else {
                        std::wcerr << L"Failed to open device handle." << std::endl;
                    }
                //}
            }
        }
        free(devInterfaceDetailData);
    }

    // 釋放設備信息集合
    SetupDiDestroyDeviceInfoList(hDevInfo);

    // 取消初始化COM
    CoUninitialize();

    return 0;
}


int GetBLEDev() {
    // 初始化COM
    HRESULT hr = CoInitializeEx(NULL, COINIT_MULTITHREADED);
    if (FAILED(hr)) {
        std::cerr << "COM initialization failed: " << std::hex << hr << std::endl;
        return 1;
    }

    // 獲取藍牙設備信息集合
    HDEVINFO hDevInfo = SetupDiGetClassDevs(&GUID_DEVCLASS_BLUETOOTH, NULL, NULL, DIGCF_PRESENT);
    if (hDevInfo == INVALID_HANDLE_VALUE) {
        std::cerr << "SetupDiGetClassDevs failed: " << GetLastError() << std::endl;
        CoUninitialize();
        return 1;
    }

    SP_DEVINFO_DATA devInfoData;
    devInfoData.cbSize = sizeof(SP_DEVINFO_DATA);

    // 遍歷設備
    for (DWORD i = 0; SetupDiEnumDeviceInfo(hDevInfo, i, &devInfoData); i++) {
        DWORD DataT;
        LPTSTR buffer = NULL;
        DWORD buffersize = 0;

        // 取得FriendlyName
        while (!SetupDiGetDeviceRegistryProperty(hDevInfo, &devInfoData, SPDRP_FRIENDLYNAME, &DataT, (PBYTE)buffer, buffersize, &buffersize)) {
            if (GetLastError() == ERROR_INSUFFICIENT_BUFFER) {
                if (buffer) LocalFree(buffer);
                buffer = (LPTSTR)LocalAlloc(LPTR, buffersize);
            }
            else {
                break;
            }
        }

        if (buffer != NULL) {
            std::wcout << L"Friendly Name: " << buffer << std::endl;
            LocalFree(buffer);

            // 獲取設備接口
            SP_DEVICE_INTERFACE_DATA interfaceData;
            interfaceData.cbSize = sizeof(SP_DEVICE_INTERFACE_DATA);
            if (SetupDiEnumDeviceInterfaces(hDevInfo, &devInfoData, &GUID_DEVCLASS_BLUETOOTH, 0, &interfaceData)) {
                DWORD requiredSize = 0;
                SetupDiGetDeviceInterfaceDetail(hDevInfo, &interfaceData, NULL, 0, &requiredSize, NULL);

                PSP_DEVICE_INTERFACE_DETAIL_DATA detailData = (PSP_DEVICE_INTERFACE_DETAIL_DATA)LocalAlloc(LPTR, requiredSize);
                detailData->cbSize = sizeof(SP_DEVICE_INTERFACE_DETAIL_DATA);

                if (SetupDiGetDeviceInterfaceDetail(hDevInfo, &interfaceData, detailData, requiredSize, NULL, NULL)) {
                    HANDLE deviceHandle = CreateFile(detailData->DevicePath, GENERIC_READ | GENERIC_WRITE, FILE_SHARE_READ | FILE_SHARE_WRITE, NULL, OPEN_EXISTING, 0, NULL);
                    if (deviceHandle != INVALID_HANDLE_VALUE) {
                        std::wcout << L"Device Handle Acquired: " << deviceHandle << std::endl;
                        // 使用deviceHandle進行操作
                        CloseHandle(deviceHandle);
                    }
                    else {
                        std::cerr << "Failed to open device handle: " << GetLastError() << std::endl;
                    }
                }
                LocalFree(detailData);
            }
        }
    }

    // 釋放設備信息集合
    SetupDiDestroyDeviceInfoList(hDevInfo);

    // 取消初始化COM
    CoUninitialize();

    return 0;
}

int arr[5];

void userInput() {
    std::cout << "Enter array elements" << std::endl;
    for (int i = 0; i < 5; i++) {
        std::cin >> arr[i];
    }
}

int CheckRSSI()
{
    // 初始化 Bluetooth
    if (WSAStartup(MAKEWORD(2, 2), NULL) != 0) {
        printf("WSAStartup failed\n");
        return 1;
    }

    BLUETOOTH_DEVICE_INFO deviceInfo = { 0 };
    deviceInfo.dwSize = sizeof(BLUETOOTH_DEVICE_INFO);

    // 輸入你的藍芽裝置地址
    // 可以在控制台 -> 裝置和印表機 -> 藍芽 找到裝置的地址
    wchar_t deviceAddress[] = L"XX:XX:XX:XX:XX:XX";

    BLUETOOTH_ADDRESS btAddress;
    if (BluetoothStringToAddress(deviceAddress, &btAddress) != 0) {
        printf("Invalid Bluetooth address\n");
        return 1;
    }

    HANDLE hRadio;
    HBLUETOOTH_RADIO_FIND hFind;
    BLUETOOTH_FIND_RADIO_PARAMS btfrp = { sizeof(BLUETOOTH_FIND_RADIO_PARAMS) };

    // 尋找藍芽適配器
    hFind = BluetoothFindFirstRadio(&btfrp, &hRadio);
    if (NULL == hFind) {
        printf("BluetoothFindFirstRadio failed\n");
        return 1;
    }

    // 取得藍芽裝置的信息
    if (BluetoothGetDeviceInfo(hRadio, &btAddress, &deviceInfo) != ERROR_SUCCESS) {
        printf("BluetoothGetDeviceInfo failed\n");
        BluetoothFindRadioClose(hFind);
        return 1;
    }

    // 打開藍芽服務
    HANDLE hDevice = BluetoothOpenDevice(&deviceInfo.Address, NULL);
    if (hDevice == NULL) {
        printf("BluetoothOpenDevice failed\n");
        BluetoothFindRadioClose(hFind);
        return 1;
    }

    // 取得RSSI
    BLUETOOTH_DEVICE_SEARCH_PARAMS searchParams = { sizeof(BLUETOOTH_DEVICE_SEARCH_PARAMS) };
    searchParams.hRadio = hRadio;
    searchParams.fIssueInquiry = TRUE;
    searchParams.cTimeoutMultiplier = 4; // TimeoutMultiplier * 1.28s

    BLUETOOTH_DEVICE_SEARCH_RESULT searchResult = { sizeof(BLUETOOTH_DEVICE_SEARCH_RESULT) };
    searchResult.szName[0] = '\0'; // 將名稱設置為空字串

    if (BluetoothPerformInquiry(hRadio, 8, 0, NULL, &searchParams, &searchResult) != ERROR_SUCCESS) {
        printf("BluetoothPerformInquiry failed\n");
        BluetoothCloseDevice(hDevice);
        BluetoothFindRadioClose(hFind);
        return 1;
    }

    printf("RSSI: %d dBm\n", searchResult.lRSSI);

    // 關閉藍芽相關處理
    BluetoothCloseDevice(hDevice);
    BluetoothFindRadioClose(hFind);
    WSACleanup();

    return 0;
}

int main()
{
    //GetHIDDev();

    //LONG rssi = 0;
    //DWORD dwSizeRssi = sizeof(rssi);
    //dwResult = WlanQueryInterface(hClient,
    //    &pIfInfo->InterfaceGuid,
    //    wlan_intf_opcode_rssi,
    //    NULL,
    //    &dwSizeRssi,
    //    (PVOID*)&rssi,
    //    &opCode);

    //if (dwResult == ERROR_SUCCESS)
    //{
    //    wprintf(L"RSSI = %u \n", rssi);
    //}


    userInput();
    return 0;
}