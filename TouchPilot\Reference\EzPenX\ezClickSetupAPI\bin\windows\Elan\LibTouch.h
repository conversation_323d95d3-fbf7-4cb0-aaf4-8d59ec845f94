
#ifndef _LIB_TOUCH_H_INCLUDE_948228FV_85JI_2964_IE94_058276KL4A52
#define _LIB_TOUCH_H_INCLUDE_948228FV_85JI_2964_IE94_058276KL4A52

// The following ifdef block is the standard way of creating macros which make exporting 
// from a DLL simpler. All files within this DLL are compiled with the ELANTPDLL_EXPORTS
// symbol defined on the command line. this symbol should not be defined on any project
// that uses this DLL. This way any other project whose source files include this file see 
// ELANTPDLL_API functions as being imported from a DLL, wheras this DLL sees symbols
// defined with this macro as being exported.

#pragma once
#include "LibTouchExp.h"
#include <Windows.h>
#include <string>
#include <vector>
#include <stdexcept>

using namespace std;


#ifdef ELANTPDLL_EXPORTS
#define ELANTPDLL_API extern "C" __declspec(dllexport) 
#else
#define ELANTPDLL_API extern "C" __declspec(dllimport)
#endif


//#ifndef BOOL
//#define BOOL bool
//#endif 
//#ifndef BYTE
//#define BYTE unsigned char
//#endif
//#ifndef PBYTE 
//#define PBYTE unsigned char*
//#endif
//#ifndef USHORT
//#define USHORT unsigned short
//#endif
//#ifndef ULONG
//#define ULONG unsigned long
//#endif 
//#ifndef tagPOINT
//typedef struct tagPOINT
//{
//    LONG  x;
//    LONG  y;
//} POINT, *PPOINT;
//#endif


////////////////////////////////////////////////////////////////////
// Since the pen report have different format by different firmware.
// The libraty will judge it first.
//

# pragma pack (1)

typedef struct _SYSDELTA_TIME {
   WORD wYear;
   WORD wMonth;
   WORD wDayOfWeek;
   WORD wDay;
   WORD wHour;
   WORD wMinute;
   WORD wSecond;
   WORD wMilliseconds;
   LONGLONG llDeltaMicroSeconds; 
} SYSDELTA_TIME;

//////////////////////////////////////////////////////////////////
// 
//
typedef struct _EMC_REPORT_PEN
{
	unsigned int uiReportID;

	bool bInRange;
	bool bTip;
	bool bBarrel;
	bool bInvert;

	bool bErase;
	bool bButton1;
	bool bButton2;
	bool bReserved;
	unsigned int uiBattery;
	unsigned int uiPosX;
	unsigned int uiPosY;
	unsigned int uiTipPressure;

	int iTiltX;	// value is from -90 to 90
	int iTiltY; 
	int iAzimuth;
}EMC_REPORT_PEN, *PEMC_REPORT_PEN;

////////////////////////////////////////////////////////////////////
// Since the pen report have different format by different firmware.
// The libraty will judge it first.
//
typedef struct _EMC_FINGER
{
	unsigned int uiContactID;
	bool bTip;
	bool bReserved[3];	
	unsigned int uiContactWidth;
	unsigned int uiContactHeight;
	unsigned int uiPosX;
	unsigned int uiPosY;
	unsigned int uiPosCenterX;
	unsigned int uiPosCenterY;

}EMC_FINGER, *PEMC_FINGER;

typedef struct _EMC_REPORT_FINGER
{
   unsigned int uiReportID;					
   unsigned int uiActiveTouchCount;   
   unsigned int uiScanTime;
   EMC_FINGER finger[10];			// 10 finger data
}EMC_REPORT_FINGER, *PEMC_REPORT_FINGER;


# pragma pack ()

//J2++
//Add the __stdcall
typedef void (__stdcall *PFUNC_REPORT_RECEIVE)(unsigned char* pReportBuf, int nReportLen, int nReportCount, SYSDELTA_TIME timeStamp);
//J2--

//J2++
typedef void (__stdcall *PFUNC_OUT_REPORT_CALLBACK)(unsigned char* pReportBuf, int nReportLen);
typedef void (__stdcall *PFUNC_IN_REPORT_CALLBACK)(unsigned char* pReportBuf, int nReportLen);
typedef void (__stdcall *PFUNC_SOCKET_EVENT_CALLBACK)(int nEvetID);
//J2--

namespace ELANAPI {

/******************************************************************
* Elan TP Library												  *
*******************************************************************
* Ver 0.0.1														  *							  
*******************************************************************/

//////////////////////////////////////////////////////////////////////////////////
// Firmware definition to enable / disable function
//
// Bit 0 Button 1 self-defined
// Bit 1 Button 2 self-defined
// Bit 2 Button 1 Switch (Barrel)
// Bit 3 Button 2 Switch (Invert)
// Bit 4 Button 1 Key define 
// Bit 5 Button 2 Key define
//////////////////////////////////////////////////////////////////////////////////

#define BIT_INVERT			0x01 
#define BIT_BARREL_SW		0x02
#define BIT_SELF_DEFINE		0x04
#define BIT_KEY_DEFINE		0x08



# pragma pack (1)
////////////////////////////////////////////////////////////////////
// The default format for touch finger and pen data format
//
typedef struct _TOUCH_PEN_DATA
{
	BYTE ReportID;				// offset 0
	BYTE bInRange : 1;			// offset 1
	BYTE bTip : 1;
	BYTE bBarrel : 1;
	BYTE bInvert : 1;
	BYTE bErase : 1;
	BYTE reserve_offset_1 : 3;
	BYTE x_position_lsbyte;		// offset 2
	BYTE x_position_msbyte;		// offset 3
	BYTE y_position_lsbyte;		// offset 4
	BYTE y_position_msbyte;		// offset 5
	BYTE tip_pressure_lsbyte;	// offset 6
	BYTE tip_pressure_msbyte;	// offset 7
	//BYTE battery;				// offset 8
	//BYTE bButton1 : 1;			// offset 9
	//BYTE bButton2 : 1;
	//BYTE reserve_offset_9 : 6;
} TOUCH_PEN_DATA, *PTOUCH_PEN_DATA;

typedef struct _TOUCH_FINGER
{
	BYTE bTip : 1;
	BYTE reserve_offset : 3;		
	BYTE contactID : 4;					// offset 0
	BYTE contact_width;					// offset 1
	BYTE contact_height;				// offset 2
	BYTE x_position_lsbyte;				// offset 3
	BYTE x_position_msbyte;				// offset 4
	BYTE x_center_position_lsbyte;		// offset 5
	BYTE x_center_position_msbyte;		// offset 6
	BYTE y_position_lsbyte;				// offset 7
	BYTE y_position_msbyte;				// offset 8
	BYTE y_center_position_lsbyte;		// offset 9
	BYTE y_center_position_msbyte;		// offset 10

}TOUCH_FINGER, *PTOUCH_FINGER;

typedef struct _TOUCH_FINGER_DATA
{
   BYTE ReportID;					// 0ffset 0
   TOUCH_FINGER finger[10];			// 0ffset 1 ~ 110
   BYTE scan_time_low_lsbyte;		// offset 111
   BYTE scan_time_low_msbyte;		// offset 112
   BYTE scan_time_high_lsbyte;		// offset 113
   BYTE scan_time_high_msbyte;		// offset 114

   BYTE active_touch_count;			// offset 115
}TOUCH_FINGER_DATA, *PTOUCH_FINGER_DATA;

# pragma pack ()

//////////////////////////////////////////////////////////////

//typedef struct
//{
//	BOOL bErase;
//	BOOL bInvert;
//	BOOL bBarrelSwitch;
//	BOOL bTipSwitch;
//	BOOL bInRange;
//	BOOL bDefBtn1;
//	BOOL bDefBtn2;
//
//	USHORT PreX;	
//	USHORT CurX;
//	USHORT PreY;	
//	USHORT CurY;
//	USHORT PreTipPressure;	
//	USHORT CurTipPressure;
//
//}PEN_REPORT;


//
// Touch report struct definition
//

//enum TOUCH_TAG
//{
//	TAG_NONE = 0,
//	TAG_DOWN = 1,
//	TAG_UP	 = 2
//};
//
//
//typedef struct
//{
//	TOUCH_TAG Tag;		//USED->1, FREE->0, UP->2
//	BOOL    bTip;
//	DWORD	ContactID;
//	POINT	PrePT;
//	POINT	CurPT;
//	POINT	PreSize;
//	POINT	CurSize;
//	INT		state;
//}TOUCH_POINT, *PTOUCH_POINT;


//typedef struct
//{
//	TOUCH_POINT TouchPoint[10];
//	LONG ScanTimeMS;
//	LONG ActiveContactNum;
//
//}TOUCH_REPORT, *PTOUCH_REPORT;

////////////////////////////////////////////////////////
// Debug and Error message functions
////////////////////////////////////////////////////////

// When API function call get error code "TP_ERR_CHK_MSG". Use this 
// function to get more error information.
ELANTPDLL_API char* __stdcall GetErrMessage(void);

// Internal coding use only. For some specail tool, set this function to
// show more information at runtime.
ELANTPDLL_API void __stdcall SetMsgOutFunc(char* sTag, void (*pFuncDebugOutStream)(char* sOutMessage, int nMessageLen));

ELANTPDLL_API const char* __stdcall GetVersionDetail(void);

////////////////////////////////////////////////////////
//Declare functions : HID basic funcitons
////////////////////////////////////////////////////////

//nInterface
// Windows:
//	-INTF_TYPE_HID_WINDOWS			= 1;
//
// Linux:
//	-INTF_TYPE_HID_LINUX			= 2;
//	-INTF_TYPE_I2C_LINUX			= 3;
//	-INTF_TYPE_I2CHID_LINUX			= 4;
//	-INTF_TYPE_I2C_CHROME_LINUX		= 5;
//	-INTF_TYPE_FW20_I2C_LINUX		= 6;
//  -INTF_TYPE_SOCKET				= 7;
//  -INTF_TYPE_BRIDGE_I2C_WINDOWS   = 8;
//  -INTF_TYPE_BRIDGE_SPI_WINDOWS   = 9;
//  -INTF_TYPE_USB_BULK_HID_SPI     = 10;
ELANTPDLL_API int __stdcall Connect(int nVID = 0x04F3, 
									int nPID = 0x00, 
									int nInterface=1, //Default is Windows HID
									int nVdd=33, 
									int nVio=33, 
									int nI2CAdr=0x20,
									int nI2CLength=0x3f,
									int nTDDISPICLKRate=0,
									unsigned char* pInterfaceParam = NULL);	// Interface has its own different parameter to use.

// For INTF_TYPE_USB_BULK_HID_SPI, High-Speed bridge ( support hid over spi and pure spi.. maybe need to supprot daisy chain and intel spi different protocol )
// pInterfaceParam : 
//						B[0] : Version (01)
//                      B[1] : Contnet Length, not includes B[0..1] 
//						B[2] : Interface Type (0: MS HID over SPI / Pure SPI, 3: Daisy Chain SPI)
//                      B[3] : iInitalMode (0: Do nothnig, 1: Pure SPI, 2: HID over SPI inital progress, 3: By hid over spi and do 
//                      B[4..7] : High Speed SPI Clock Frequency in Hz
//                      B[8] : SPI Mode (CPHA CPOL = 0x00, 0x01, 0x02 or 0x03)
//						B[9] : INTR Check initial state ( 0: Disable. 1:Enable )
//
//

//J2++
//Connect to TP via socket.
//nIPAddress : 0 run server. other run client. format : 127.0.0.1 => 7f000001
#if defined(INTERFACE_SOCKET)
ELANTPDLL_API int __stdcall ConnectSocket(int nIPAddress, int nPort, PFUNC_SOCKET_EVENT_CALLBACK pFuncSocketEvent = NULL);
#endif
//J2--
ELANTPDLL_API int __stdcall ConnectBridge(int nVID, int nPID, TP_INTERFACE_TYPE eInterfaceType);

ELANTPDLL_API int __stdcall ResetBridge(int nVID, int nPID, TP_INTERFACE_TYPE eInterfaceType);

// This function only send power on command and use to rescue 66X0 series 
ELANTPDLL_API int __stdcall RescueConnect(int nVID = 0x04F3, 
									int nPID = 0x00, 
									int nInterface=1,
									int nVdd=33, 
									int nVio=33, 
									int nI2CAdr=0x20,
									int nI2CLength=0x3f,
									int nSPICLKRate=0);

//ELANTPDLL_API int ConnectByDevPath(int nVID, int nPID, char *pszDevPath);

/*!
	Close the connection wiht HID Device
*/
ELANTPDLL_API int __stdcall Disconnect();

ELANTPDLL_API int __stdcall IsHIDI2CConnected(int nTimeout, int nDevIdx);

// Alan 20210810
ELANTPDLL_API int __stdcall SetSPICmdLength(int nCmdLength = 0x06);
//~Alan 20210810

//ELANTPDLL_API void DisconnectByDevPath(char *pszDevicePath);

/*!
	Get the connected and support Device Count
	\return return the connected HID Device Count 
*/
ELANTPDLL_API int __stdcall GetDeviceCount(void);

/*!
	Connect to indicated HID Device
   @param[out] pszDevPairNum the char array that stores the HID Device Pair Number
   @param[in] nDevIdx the index of HID Device
 */
ELANTPDLL_API int __stdcall GetHIDDevPairNum(char *pszDevPairNum, int strlen, int nDevIdx);

/*!
	Connect to indicated HID Device
   @param[out] 
   @param[in] nDevIdx the index of HID Device
 */
ELANTPDLL_API int __stdcall GetHIDDevPath(char *pszDevPath, int strlen, int nDevIdx);

/*!
   Get the device type by usage_page and usage
   @param[out] 
   @param[in] nDevIdx the index of HID Device
 */
ELANTPDLL_API int __stdcall GetHIDDevType(char *pszDevType, int strlen, int nDevIdx);

ELANTPDLL_API int __stdcall GetPenLogicalSize(int &nX, int &nY, int nDevIdx);
ELANTPDLL_API int __stdcall GetProfile(PTOUCH_PROFILE pProfile, int nDevIdx = 0);

/*!
   Reset system timer resolution. Defaultly, the system timer resolution will be reset to 1 ms. 
   If app doesn't want to reset system timer resolution, call this function first before connect(...)
   @param[in] bResetEnable, false to disable reset method.
   @param[in] nTimerMS, the new timer resolution.
 */
ELANTPDLL_API void __stdcall SetSysTimerResolution(bool bResetEnable, int nTimerMS = 1);

//////////////////////////////////////////////////////////////////
// Use callback functon to export the debug message to console 
// or any other output string control unit, like Windows CtrlEdit.
// 

//J2++
ELANTPDLL_API int __stdcall OutReportRegCallback(PFUNC_OUT_REPORT_CALLBACK pFuncReportRcv);
ELANTPDLL_API int __stdcall OutReportUnRegCallback();

ELANTPDLL_API int __stdcall InReportRegCallback(PFUNC_IN_REPORT_CALLBACK pFuncReportRcv);
ELANTPDLL_API int __stdcall InReportUnRegCallback();
//J2--

ELANTPDLL_API int __stdcall InputRawRegHIDCallback(PFUNC_REPORT_RECEIVE pFuncReportRcv, int nDevIdx = 0, int nUsagePage = 0x0D, int Usage = 0x00);
ELANTPDLL_API int __stdcall InputRawUnRegHIDCallback(int nDevIdx = 0);

ELANTPDLL_API int __stdcall InputRawRegHIDDevice(HWND hWnd, int nDevIdx = 0, int nUsagePage = 0x0D, int Usage = 0x00);

ELANTPDLL_API int __stdcall InputRawGetReport(WPARAM wParam, LPARAM lParam, BYTE (&pFingerReport)[MAX_TOUCH_REPORT_NUM][MAX_TOUCH_REPORT_LEN], int& nPacketCnt);

ELANTPDLL_API int __stdcall InputRawGetData(WPARAM wParam, LPARAM lParam, BYTE (&pFingerReport)[MAX_TOUCH_REPORT_NUM][MAX_TOUCH_REPORT_LEN], int &nPacketCnt);

//////////////////////////////////////////////////////////////////
// When the appication get the data from WM_INPUT, call theose 
// function to parse the dat. Since the data could be in different
// format by different firmware, we define the data in library.
//
ELANTPDLL_API int __stdcall ParserReportPen(unsigned char *pData, int iDataLen, PEMC_REPORT_PEN p_rpPen, int nDevIdx = 0);

ELANTPDLL_API int __stdcall ParserReportFinger(unsigned char *pData, int iDataLen, PEMC_REPORT_FINGER p_rpFinger, int nDevIdx = 0);

//////////////////////////////////////////////////////////////////
// Use windows user defined message to acknowledge the caller
// about the battery power status
ELANTPDLL_API int __stdcall DoBatteryCheck(unsigned int uiMessage, int nDevIdx = 0);

////////////////////////////////////////////////////////
// Send command 
////////////////////////////////////////////////////////
ELANTPDLL_API int __stdcall SendDevCommand(unsigned char *pszCommandBuf, int nCommandLen,  int nTimeoutMS = -1, int nDevIdx = 0);// buf[1]=0x00&buf[2]=length

ELANTPDLL_API int __stdcall ReadDevData(unsigned char *pszDataBuf,int nDataLen, int nTimeoutMS = -1, int nDevIdx = 0);

ELANTPDLL_API int __stdcall SendBridgeCommand(unsigned char *pszCommandBuf, int nCommandLen,  int nTimeoutMS = -1, int nDevIdx = 0);

// Alan 20191224
ELANTPDLL_API int __stdcall SetPenFeatureCommand(unsigned char* pszCommandBuf, int nCommandLen, int nTimeOutMS = -1, int nDevIdx = 0);

ELANTPDLL_API int __stdcall GetPenFeatureCommand(unsigned char* pszDataBuf, int nRcvDataLen, int nTimeOutMS = -1, int nDevIdx = 0);
//~Alan 20191224

#ifndef LIBUSB0_NOT_SUPPORT
//J2++
//#if !defined(INTERFACE_SOCKET)
ELANTPDLL_API bool __stdcall IsSupportBulk();
ELANTPDLL_API int __stdcall SendBulkCommand(unsigned char *pszCommandBuf, int nCommandLen, int nTimeoutMS = -1, int nDevIdx = 0);
//#endif
//J2--
#endif


////////////////////////////////////////////////////////
// High-Speed Bridge - Bulk SP Controller
////////////////////////////////////////////////////////
#ifndef LIBUSB0_NOT_SUPPORT
// Caro ++ 2023/07/24
//ELANTPDLL_API int __stdcall BRDG_GetFirmwareVer(unsigned int& rFWVer);
//ELANTPDLL_API int __stdcall BRDG_INTR_StateGet(unsigned char* pState);

ELANTPDLL_API int __stdcall BRDG_BulkOutCmd(unsigned char *pszCommandBuf, int nCommandLen, int nTimeoutMS = -1, int nDevIdx = 0);
ELANTPDLL_API int __stdcall BRDG_BulkInControl(unsigned char* pBuf, int iBufByteSz, int nTimeoutMS = -1, int nUSBDevIdx = 0);
ELANTPDLL_API int __stdcall BRDG_BulkInData(unsigned char* pBuf, int iBufByteSz, int nTimeoutMS = -1, int nUSBDevIdx = 0);

ELANTPDLL_API int __stdcall BRDG_SPICtrl_MOSIDataSet(unsigned char* pDataMOSI, int iDataLen, int nTimeOutMS = -1, int nDevIdx = 0);
ELANTPDLL_API int __stdcall BRDG_SPICtrl_MISODataGet(unsigned char* pDataMISO, int iDataLen);
ELANTPDLL_API int __stdcall BRDG_SPICtrl_Execute( int iDataLen, int nTimeOutMS = -1, int nDevIdx = 0);

ELANTPDLL_API int __stdcall DVIS_HIDovSPI_SetOutputReport(int iReportID, unsigned char* pData, int iDataCnt, int nTimeoutMS = -1, int nUSBDevIdx = 0);
ELANTPDLL_API int __stdcall DVIS_HIDovSPI_GetInputReport(int iReportID, int nTimeoutMS = -1, int nUSBDevIdx = 0);
ELANTPDLL_API int __stdcall DVIS_HIDovSPI_IntInputReport(int& iReportID, int& iContentLen, unsigned char* pBuf, int iBufByteSz, int nTimeoutMS = -1, int nUSBDevIdx = 0);

// Caro --
// 
#endif
////////////////////////////////////////////////////////
// Get device information
////////////////////////////////////////////////////////

ELANTPDLL_API int __stdcall GetID(unsigned int* p_nVID, unsigned int* p_nPID, int nDevIdx = 0);

ELANTPDLL_API int __stdcall GetFwIDEx(unsigned int* fwid, int nTimeoutMS = 1000, int nDevIdx = 0);

ELANTPDLL_API int __stdcall GetFwVerEx(unsigned int* fwVer, int nTimeoutMS = 1000, int nDevIdx = 0);

ELANTPDLL_API int __stdcall GetBridgeVersion(unsigned int* p_nbridgeVer, int nDevIdx = 0);

//J2++
/*!
   Get TP Test Solution Version
   @param[in] nTimeoutMS Timeout for getting data, default is set to 1000
   @param[in] nDevIdx The HID Device Index, default is set to 0
   \return Test Version
 */
ELANTPDLL_API int _stdcall GetTestSolVerEx(unsigned int* pTestSolVer, int nTimeoutMS = 1000, int nDevIdx = 0);

/*!
   Get TP Test Version
   @param[in] nTimeoutMS Timeout for getting data, default is set to 1000
   @param[in] nDevIdx The HID Device Index, default is set to 0
   \return Test Version
 */
ELANTPDLL_API int __stdcall GetTestVersion(int nTimeoutMS = 1000, int nDevIdx = 0);
ELANTPDLL_API int __stdcall GetTestVerEx(unsigned int* pTestVer, int nTimeoutMS = 1000, int nDevIdx = 0);

ELANTPDLL_API int __stdcall GetSolutionVersion(int nTimeoutMS = 1000, int nDevIdx = 0);
ELANTPDLL_API int __stdcall GetSolVerEx(unsigned int* pSolVer, int nTimeoutMS = 1000, int nDevIdx = 0);

/*!
   Get TP BootCode Version
   @param[in] nDevIdx The HID Device Index, default is set to 0
   \return BootCode Version
 */
ELANTPDLL_API int __stdcall GetBootCodeVersion(int nTimeoutMS = 1000, int nDevIdx = 0);
ELANTPDLL_API int __stdcall GetBCVerEx(unsigned int* pBCVer, int nTimeoutMS = 1000, int nDevIdx = 0);

/*!
   Get TP WHCK Version
   @param[in] nDevIdx The HID Device Index, default is set to 0
   \return WHCK Version
 */
ELANTPDLL_API int __stdcall GetWHCKVersion(int nTimeoutMS = 1000, int nDevIdx = 0);
ELANTPDLL_API int __stdcall GetWHCKVerEx(unsigned int* pWHCKVer, int nTimeoutMS = 1000, int nDevIdx = 0);

/*!
   Get TP Param Tune Version
   @param[in] nDevIdx The HID Device Index, default is set to 0
   \return WHCK Version
 */
ELANTPDLL_API int __stdcall GetParamTuneVersion(int nTimeoutMS = 1000, int nDevIdx = 0);
ELANTPDLL_API int __stdcall GetParamTuneVersionEx(unsigned int* pParamTuneVer, int nTimeoutMS = 1000, int nDevIdx = 0);
ELANTPDLL_API int __stdcall GetSensorID(unsigned int *pSensorID, int nTimeoutMS = 1000, int nDevIdx = 0);
ELANTPDLL_API int __stdcall GetSensorIDRAM(unsigned int *pValue, unsigned short nAddr, int nTimeoutMS = 1000, int nDevIdx = 0);
//J2--

// Alan 20180409
ELANTPDLL_API int __stdcall GetICOtherInfoEx(unsigned int* pInfo, int nAddress,int nTimeoutMS = 1000, int nDevIdx = 0);
ELANTPDLL_API int __stdcall GetSNVerEx(unsigned int* pSNVer, int nTimeoutMS = 1000, int nDevIdx = 0);
//~Alan 20180409

// Alan 20220524/20220713
// For Gen8, Get Solution Version, Test Version, Codebase Version, Parameter Value, Parameter Def
ELANTPDLL_API int __stdcall GetGen8SolutionVerEx(unsigned int* p_nSolutionVer, int nTimeoutMS = -1, int nDevIdx = 0);
ELANTPDLL_API int __stdcall GetGen8TestVerEx(unsigned int* p_nTestVer, int nTimeoutMS = -1, int nDevIdx = 0);
ELANTPDLL_API int __stdcall GetGen8CodebaseVerEx(unsigned int* p_nCodebaseVer, int nTimeoutMS = -1, int nDevIdx = 0);
ELANTPDLL_API int __stdcall GetGen8ParameterValueEx(unsigned int* p_nParamValue, int nTimeoutMS = -1, int nDevIdx = 0);
ELANTPDLL_API int __stdcall GetGen8ParameterDefEx(unsigned int* p_nParamDef, int nTimeoutMS = -1, int nDevIdx = 0);
//~Alan 20220524/20220713

/*!
   Get TP Bridge Version
   @param[in] nDevIdx The HID Device Index, default is set to 0
   \return Bridge Version
 */
//ELANTPDLL_API int GetBridgeVersion(int nDevIdx = 0);

ELANTPDLL_API int __stdcall GetTPInfo(CTPInfo &TPInfo, int nPartial, int nTimeoutMS = -1, int nDevIdx = 0);

ELANTPDLL_API int __stdcall GetChipNum(int nPartial, int nTimeoutMS = -1, int nDevIdx = 0);

/*!
   Get trace information with get TP information
   @param[in/out] pTraceInfo
   @param[in] nDefaultPartial
   @param[in] nTimeoutMS
   @param[in] nDevIdx
   \return Result
 */
ELANTPDLL_API int __stdcall GetTraceInfo(TraceInfo *pTraceInfo, int nDefaultPartial, int nTimeoutMS, int nDevIdx);

ELANTPDLL_API int __stdcall SetTraceInfo(TraceInfo *pTraceInfo);

/*!
   Get the RX Trace number of Master, Slave1, Slave2
   @param[in] pTraceArray
   @param[in] nPartial
   @param[in] nTimeoutMS
   @param[in] nDevIdx
   \return Result
 */
ELANTPDLL_API int __stdcall GetRXTraceArray(int *pTraceArray, int nPartial, int nTimeoutMS = -1, int nDevIdx = 0);
ELANTPDLL_API int __stdcall GetRXTrace(int nPartial, int nTimeoutMS = -1, int nDevIdx = 0);
ELANTPDLL_API int __stdcall GetRXTraceEx(int* p_nRxTrace, int nTimeoutMS = -1, int nDevIdx = 0);

ELANTPDLL_API int __stdcall GetTXTrace(int nPartial, int nTimeoutMS = -1, int nDevIdx = 0);
ELANTPDLL_API int __stdcall GetTXTraceEx(int* p_nTxTrace, int nTimeoutMS = -1, int nDevIdx = 0);

/*!
	Do Calibration
	@param[in] nTimeout	The timeout for waiting the ReK finish (unit : seconds, recommand: 3 sec.)
	@param[in] bOldRek	TRUE:use the old method to do calibration, FALSE:use the new method to do calibration(recommand)
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return return the result
 */
ELANTPDLL_API int __stdcall ReK( int nTimeout, bool bOldRek, int nDevIdx = 0);

//ELANTPDLL_API int UsbResetDevice(int nDevIdx = 0);

ELANTPDLL_API bool __stdcall GetPage(unsigned short PageBuffer[], unsigned short nAddr, int nSize, int nTimeoutMS = -1, int nDevIdx = 0, bool bFor63XXInfoPage = false);

ELANTPDLL_API bool __stdcall GetEEPROMData(unsigned char* pROMBuffer, int nSize, int nTimeoutMS = -1, int nDevIdx = 0);
ELANTPDLL_API int __stdcall SetEEPROMData(unsigned char* pROMBuffer, int nSize, int nTimeoutMS = -1, int nDevIdx = 0);

/*!
	Enable/Disable Touch IC Test Mode
	@param[in] bEnable Enable or Disable Test Mode
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return return the result
 */
ELANTPDLL_API int __stdcall EnableTestMode(bool bEnable, int nTimeoutMS = -1, int nDevIdx = 0);

ELANTPDLL_API int __stdcall EnableCCVMode(bool bEnable, bool bEnableSwitch, int nDelay, int nTimeoutMS = -1, int nDevIdx = 0);

ELANTPDLL_API int __stdcall Polling(int nTimeoutMS = -1, int nDevIdx = 0);

/*!
	Enable/Disable Touch IC Test Mode
	@param[in] bEnable Enable or Disable Sensor Check Mode
	@param[in] nTimeout The timeout for waiting the ReK finish (unit : seconds, recommand: 3 sec.)
	@param[in] bOldReK use the old method to do calibration(recommand to set false)
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return return the result
 */
//ELANTPDLL_API int EnableSensorCheck( bool bEnable, int nTimeout,bool bOldReK, int nDevIdx = 0);

/*!
	Set Pen Button Mode
	@param[in] Btn0 could be set to INVERT / BARREL SWITCH / SELF-DEFINE / KEY-DEFINE
	@param[in] Btn1 could be set to INVERT / BARREL SWITCH / SELF-DEFINE / KEY-DEFINE
 */

// We do only allow one mode at one time. For example, (BIT_INVERTT | BIT_BARREL_SW) as input
// parameter is not leagel usage. 
#define BIT_INVERT			0x01 
#define BIT_BARREL_SW		0x02
#define BIT_SELF_DEFINE		0x04
#define BIT_KEY_DEFINE		0x08

ELANTPDLL_API int __stdcall PenSetBtnMode( unsigned char btn1, unsigned char btn2, int nTimeoutMS = -1, int nDevIdx = 0);
	
ELANTPDLL_API int __stdcall PenGetBtnMode( unsigned char* pBtn1, unsigned char* pBtn2, int nTimeoutMS = -1, int nDevIdx = 0);

ELANTPDLL_API int __stdcall PenSetKeyCode( int nBtnIdx, unsigned char keyCode1, unsigned char keyCode2, int nTimeoutMS = -1, int nDevIdx = 0);

ELANTPDLL_API int __stdcall PenSetTiltValue( int iTiltX, int iTiltY, int iAzimuth, int nTimeoutMS = -1, int nDevIdx = 0);

/*!
	Set RawDataCount for Combo Type
	@param[in] nCount : set 0 for Normal Type and set 3 for Combo Type
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return return result
 */
ELANTPDLL_API int __stdcall SetRawDataCount(int nCount = 0, int nTimeoutMS = 1000, int nDevIdx = 0);

/*!
	Get the default Volt Level
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return return Volt Level
 */
//ELANTPDLL_API int GetVoltLevel(int nDevIdx = 0);
ELANTPDLL_API int __stdcall GetVH(int* p_nVoltLevel, int nTimeoutMS = 1000, int nDevIdx = 0);
/*!
	Set the volt level to IC
	@param[in] bVoltLevel Volt Level(3300:1~8, 3148:1~5)
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return return result
 */
//ELANTPDLL_API int SetVoltLevel(byte bVoltLevel, int nDevIdx = 0);
ELANTPDLL_API int __stdcall SetVH(int nVoltLevel, int nTimeoutMS = 1000, int nDevIdx = 0);

ELANTPDLL_API int __stdcall GetADC1DArray(int *pFrame, int nXLen, int nYLen, int nBaseLen, unsigned char nGetTraceType, 
																							int nTimeout, int nDevIdx = 0);

ELANTPDLL_API int __stdcall GetNoise1DArray(int *pFrame, int nXLen, int nYLen, int nBaseLen, 
																unsigned char nGetTraceType, int nTimeout, int nDevIdx = 0 );

ELANTPDLL_API int __stdcall GetPenRaw1DArray(int *pFrameBuf, int nfrmXLen, int nfrmYLen, int nChannel_Num, int nTimeoutMS = -1,int nDevIdx = 0);

/*!
	Get one frame ADC Data
	@param[out] pTotalRawData the 2-d array which stores the ADC Data
	@param[in] TPInfo The IC Information
	@param[in] nTimeout Get ADC Frame Timeout(unit:ms)
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return the result
 */
ELANTPDLL_API int __stdcall GetADC(int (&pTotalRawData)[MAX_LEN][MAX_LEN], CTPInfo TPInfo, 
													unsigned char nGetTraceType, int nTimeoutMS = 10000, int nDevIdx = 0);

//J2++
/*!
	Get one frame Self ADC Data
	@param[out] pRXSelf the 1-d array which stores the TX Self ADC Data
	@param[out] pTXSelf the 1-d array which stores the RX Self ADC Data
	@param[in] TPInfo The IC Information
	@param[in] nGetTraceType The data type that need to get.
	@param[in] nTimeout Get Self ADC Frame Timeout(unit:ms)
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return the result
 */
ELANTPDLL_API int __stdcall GetSelfADC(int (&pRXSelf)[MAX_LEN], int (&pTXSelf)[MAX_LEN], CTPInfo TPInfo, 
														unsigned char nGetTraceType, int nTimeoutMS = 10000, int nDevIdx = 0);
//J2--

/*!
	Get one frame Noise Data
	@param[out] pTotalRawData the 2-d array which stores the Noise Data
	@param[in] TPInfo The IC Information
	@param[in] nTimeout Get Noise Frame Timeout(unit:ms)
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return the result
 */
ELANTPDLL_API int __stdcall GetNoise(int (&pTotalRawData)[MAX_LEN][MAX_LEN], CTPInfo TPInfo, 
											unsigned char nGetTraceType, int nTimeoutMS = 10000, int nDevIdx = 0);

	/*!
	Get one frame Base Data
	@param[out] pTotalRawData the 2-d array which stores the ADC Data
	@param[in] TPInfo The IC Information
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return the result
 */
ELANTPDLL_API int __stdcall GetBase(int (&pTotalRawData)[MAX_LEN][MAX_LEN], CTPInfo TPInfo, 
											unsigned char nGetTraceType, int nTimeoutMS = 10000, int nDevIdx = 0);
ELANTPDLL_API int __stdcall GetBase1DArray(int *pBaseData, int nXLen, int nYLen, int nBaseLen, unsigned char nGetTraceType, 
																								int nTimeoutMS, int nDevIdx = 0);


ELANTPDLL_API int __stdcall GetDV1DArray(int *pDVData, int nXLen, int nYLen, int nBaseLen, unsigned char nGetTraceType, 
																								 int nTimeoutMS, int nDevIdx = 0);
/*!
	Get one frame DV Data
	@param[out] pTotalRawData the 2-d array which stores the ADC Data
	@param[in] TPInfo The IC Information
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return the result
 */
ELANTPDLL_API int __stdcall GetDV(int (&pTotalRawData)[MAX_LEN][MAX_LEN], CTPInfo TPInfo, 
														unsigned char nGetTraceType, int nTimeoutMS = 10000, int nDevIdx = 0);

ELANTPDLL_API int __stdcall GetCCV(unsigned char *pCCVRawData, int nTPMasterNum, int nTPSlaveNum, 
																int nSampleNum, int nBaseLen, int nTimeoutMS = 10000, int nDevIdx = 0);

ELANTPDLL_API int __stdcall GetOffset1DArray(int *pDVData, int nXLen, int nYLen, int nBaseLen, unsigned char nGetTraceType, 
																								int nTimeoutMS, int nDevIdx);
ELANTPDLL_API int __stdcall GetMMTPGA1DArray(int *pDVData, int nXLen, int nYLen, int nBaseLen, unsigned char nGetTraceType, 
																								int nTimeoutMS, int nDevIdx);

/*!
	Get Multi-frame ADC Data(Use New Command Flow)
	@param[out] pTotalRawData the 2-d array which stores the ADC Data
	@param[in] TPInfo The IC Information
	@param[in] nFrameCount The frame count to get
	@param[in] nTimeout unit(ms)
	@param[in] bSensorCheck false : Get Normal ADC Data; true: Get Sensor Check Data
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return the result
 */
ELANTPDLL_API int __stdcall GetMultiADCFrame(int (&pTotalRawData)[MAX_LEN][MAX_LEN], CTPInfo TPInfo, 
											int nFrameCount, int nTimeout, bool bSensorCheck, int nDevIdx = 0);

ELANTPDLL_API int __stdcall GetMultiADCFarmes(int *pFrame, int nXLen, int nYLen, int nBaseLen, int nFrameCount, 
																	int nTimeout, int nDevIdx);

// Alan 20191016
ELANTPDLL_API int __stdcall GetLimitAreaBase1DArray(int *pLABaseData, int nFromX, int nToX, int nFromY, int nToY, int nBaseLen, int nTimeoutMS, int nDevIdx = 0);

ELANTPDLL_API int __stdcall GetLimitAreaDV1DArray(int *pLADVData, int nFromX, int nToX, int nFromY, int nToY, int nBaseLen, int nTimeoutMS, int nDevIdx = 0);

ELANTPDLL_API int __stdcall GetLimitAreaADC1DArray(int *pLAADCData,  int nFromX, int nToX, int nFromY, int nToY, int nBaseLen, int nTimeoutMS, int nDevIdx = 0);
//~Alan 20191016

// J2++ 20201112
//#if !defined(INTERFACE_SOCKET)
ELANTPDLL_API int __stdcall GetBulkTestMode1DArrayData(int *pFrame, int nXLen, int nYLen, int nBaseLen, int nDataType, unsigned char nGetTraceType, int nTimeoutMS, int nDevIdx);
//#endif
// J2--

/*!
	Get the Disc Data of one frame
	@param[out] pTotalRawData the 2-d array which stores the Disc Data
	@param[in] TPInfo The IC Information
	@param[in] pszMappingTableFile Disc Mapping Table File Name
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return the result
 */
ELANTPDLL_API int __stdcall GetDisc(byte (&pTotalRawData)[MAX_LEN][MAX_LEN], CTPInfo &TPInfo, char *pszMappingTableFile, int nTimeoutMS, int nDevIdx);

/*!
	Get the DVH Data 
	@param[out] pVH1Data the 2-d array which stores the VH1 Data
	@param[out] pVH2Data the 2-d array which stores the VH2 Data
	@param[out] pDVHData the 2-d array which stores the DVH Data
	@param[in] TPInfo The IC Information
	@param[in] nGetFrameCount Get the VH Frame Count(need >= 2)
	@param[in] nSkipFrameCount Skip the VH Frame Count (need >= 2)
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return the result
 */
ELANTPDLL_API int __stdcall GetDVH(int (&pVH1Data)[MAX_LEN][MAX_LEN], int (&pVH2Data)[MAX_LEN][MAX_LEN], 
						 int (&pDVHData)[MAX_LEN][MAX_LEN], CTPInfo &TPInfo, int nGetFrameCount, int nSkipFrameCount, int nTimeoutMS, int nDevIdx);

//J2++
/*!
	Disable the TP Report
	@param[in] bEnable true to disable TP Report;false to enable TP Report
	@param[in] nDevIdx The HID Device Index, default is set to 0
 */
ELANTPDLL_API int __stdcall DisableTPReport(bool bDisable, int nTimeoutMS = 1000, int nDevIdx = 0);
ELANTPDLL_API int __stdcall DisableActivePen(bool bDisable, int nTimeoutMS = 1000, int nDevIdx = 0);

//J2++
ELANTPDLL_API int __stdcall BlockTPRegion(bool bDisable, int nYStart, int nYEnd, int nTimeoutMS = 1000, int nDevIdx = 0);
//J2--

ELANTPDLL_API int __stdcall ClearFR(int nTimeoutMS = 1000, int nDevIdx = 0);

//The following functions declare for setting the Gain/C_int
//Get the analog paramter ANA_CTL
const unsigned char CTL_1 = 0xb8;
const unsigned char CTL_2 = 0xb9;
ELANTPDLL_API int __stdcall GetANA_CTL(unsigned char nCTLType, int &nVlaue, int nTimeoutMS = 1000, int nDevIdx = 0);
ELANTPDLL_API int __stdcall SetANA_CTL(unsigned char nCTLType, int nVlaue, int nTimeoutMS = 1000, int nDevIdx = 0);

ELANTPDLL_API int __stdcall GetBSH(int &nValue, int nTimeoutMS, int nDevIdx);
ELANTPDLL_API int __stdcall SetBSH(int nValue, int nTimeoutMS, int nDevIdx);

/*!
	Get the RGM
	@param[out] the value of RGM
	@param[in] nTimeoutMS Get data timeout, default is 1000(ms)
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return function result
*/
ELANTPDLL_API int __stdcall GetRGM(unsigned char &nValue, int nTimeoutMS, int nDevIdx);

/*!
	Set the RGM
	@param[in] the value of RGM
	@param[in] nTimeoutMS Get data timeout, default is 1000(ms)
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return function result
*/
ELANTPDLL_API int __stdcall SetRGM(unsigned char nValue, int nTimeoutMS, int nDevIdx);

/*!
	Get the PH1
	@param[in] nTimeoutMS Get data timeout, default is 1000(ms)
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return function result
*/
ELANTPDLL_API int __stdcall GetPH1(int nTimeoutMS = 1000, int nDevIdx = 0);
ELANTPDLL_API int __stdcall SetPH1(int nValue, int nTimeoutMS = 1000, int nDevIdx = 0);

/*!
	Get the PH2
	@param[in] nTimeoutMS Get data timeout, default is 1000(ms)
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return function result
*/
ELANTPDLL_API int __stdcall GetPH2(int nTimeoutMS = 1000, int nDevIdx = 0);
ELANTPDLL_API int __stdcall SetPH2(int nValue, int nTimeoutMS = 1000, int nDevIdx = 0);

/*!
	Get the PH3
	@param[in] nTimeoutMS Get data timeout, default is 1000(ms)
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return function result
*/
ELANTPDLL_API int __stdcall GetPH3(int nTimeoutMS = 1000, int nDevIdx = 0);
ELANTPDLL_API int __stdcall SetPH3(int nValue, int nTimeoutMS = 1000, int nDevIdx = 0);
/*!
	Let the Analogy parameter to take effect
	@param[in] nTimeoutMS Set data timeout, default is 1000(ms)
	@param[in] nDevIdx The HID Device Index, default is set to 0
*/
ELANTPDLL_API void __stdcall SetTPParameter(int nTimeoutMS = 1000, int nDevIdx = 0);

/*!
	Get the projection option form TP
	@param[in] nTimeoutMS Set data timeout, default is 1000(ms)
	@param[in] nDevIdx The HID Device Index, default is set to 0
*/
ELANTPDLL_API int __stdcall GetProjOption(int nTimeoutMS = 1000, int nDevIdx = 0);

ELANTPDLL_API int __stdcall SetProjOption(unsigned int nValue, int nTimeoutMS = 1000, int nDevIdx = 0);

ELANTPDLL_API int __stdcall DisableAlgorithm(bool bDisableOBL, int nTimeoutMS = 1000, int nDevIdx = 0);

ELANTPDLL_API int __stdcall GetSUM(int nTimeoutMS = 1000, int nDevIdx = 0);

ELANTPDLL_API int __stdcall SetSUM(unsigned int nValue, int nTimeoutMS = 1000, int nDevIdx = 0);

ELANTPDLL_API int __stdcall SetCH(unsigned int nValue, int nTimeoutMS = 1000, int nDevIdx = 0);

/*!
	Set the Enable/Disable Raw data mode
	When the raw data mode is enable, set the scan TX index
	@param[in] bEnable 
	@param[in] nScanTXIdx
	@param[in] nTimeoutMS Set data timeout, default is 1000(ms)
	@param[in] nDevIdx The HID Device Index, default is set to 0
*/
ELANTPDLL_API int __stdcall SetRawMode(bool bEnable, unsigned char nScanTXIdx, int nTimeoutMS = 1000, int nDevIdx = 0);
//J2--

ELANTPDLL_API int __stdcall SetDiscMode(bool bEnable, int nTimeoutMS = 1000, int nDevIdx = 0);

/*!
	Get the Algorithm
	@param[in] nDevIdx The HID Device Index, default is set to 0
	\return function result
*/
ELANTPDLL_API int __stdcall GetFWIPOption(int &nValue, int nTimeoutMS = 1000, int nDevIdx = 0);
ELANTPDLL_API int __stdcall SetFWIPOption(int nValue, int nTimeoutMS = 1000, int nDevIdx = 0);

/*!
	Set the all disc value to a indicated number.
	@param[in] nDiscValue Disc Value
	@param[in] nDevIdx The HID Device Index, default is set to 0
*/
//ELANTPDLL_API void SetDiscValue(int nDiscValue, int nDevIdx = 0);

/*!
	Transfer the nromal ADC Frame data and compute the normalized frame
	@param[in] FrameData ADC Frame
	@param[out] pNormalizedFrame store the Normalized data
	@param[in] TPInfo IC Information
*/
//ELANTPDLL_API void GetNormalization(int FrameData[MAX_LEN][MAX_LEN], int (&pNormalizedFrame)[MAX_LEN][MAX_LEN], CTPInfo &TPInfo);

/*!
	Along the TX, Find the max and min value in each RX Trace. Compute the difference between max and min. 
	Also compute the average difference between max and min of all RX Trace. The value is threashold factor.
	@param[in] NormalizedFrame The Normalization ADC Data
	@param[out] pDevAlongTXData The array store the difference between max and min in each RX Trace
	@param[out] fThresholdFactor Store the threshold factor
	@param[in] TPInfo IC Information
*/
//ELANTPDLL_API void GetHBoundDevAlongTX(int NormalizedFrame[MAX_LEN][MAX_LEN], int (&pDevAlongTXData)[MAX_LEN], 
//																			float &fThresholdFactor, CTPInfo &TPInfo);

/*!
	Along the RX, Find the max and min value in each TX Trace. Compute the difference between max and min of each TX Trace. 
	Also compute the average difference between max and min of all TX Trace. The value is threashold factor.
	@param[in] NormalizedFrame The Normalization ADC Data
	@param[out] pDevAlongRXData The array store the difference between max and min in each TX Trace
	@param[out] fThresholdFactor Store the threshold factor
	@param[in] TPInfo IC Information
*/
//ELANTPDLL_API void GetHBoundDevAlongRX(int NormalizedFrame[MAX_LEN][MAX_LEN], int (&pDevAlongRXData)[MAX_LEN], 
//																			float &fThresholdFactor, CTPInfo &TPInfo);

/*!
	Along the TX axis, compute the differ of neignbor RX Point. Also compute average Sum of two Neighbor RX Trace as threshold factor.
	@param[in] NormalizedFrame The Normalization ADC Data
	@param[out] pDiffAlongTXData 2-D Array stores the differ of Neighbor RX points.
	@param[out] pThFactorArray 1-D Array stores the average Sum of Neighbor RX Trace.
	@param[in] TPInfo IC Information
*/
//ELANTPDLL_API void GetHBoundDiffAlongTX(int NormalizedFrame[MAX_LEN][MAX_LEN], int (&pDiffAlongTXData)[MAX_LEN][MAX_LEN], 
//																			float (&pThFactorArray)[MAX_LEN], CTPInfo &TPInfo);
/*!
	Along the RX axis, compute the differ of neignbor TX Point. Also compute average Sum of two Neighbor TX Trace as threshold factor.
	@param[in] NormalizedFrame NormalizedFrame The Normalization ADC Data
	@param[out] pDiffAlongRXData 2-D Array stores the differ of Neighbor RX points.
	@param[out] pThFactorArray 1-D Array stores the average Sum of Neighbor RX Trace.
	@param[in] TPInfo IC Information
*/
//ELANTPDLL_API void GetHBoundDiffAlongRX(int NormalizedFrame[MAX_LEN][MAX_LEN], int (&pDiffAlongRXData)[MAX_LEN][MAX_LEN], 
//																				float (&pThFactorArray)[MAX_LEN], CTPInfo &TPInfo);
/*!
	Get the Differ VH Shadow Frame Data
	@param[in] VH1FrameData The VH1 ADC Frame
	@param[in] VH2FrameData The VH2 ADC Frame
	@param[out] pVHSData Store the VHS Data
	@param[in] TPInfo IC Informaiton
*/
//ELANTPDLL_API void GetDVHS(int VH1FrameData[MAX_LEN][MAX_LEN], int VH2FrameData[MAX_LEN][MAX_LEN], int (&pVHSData)[MAX_LEN][MAX_LEN], CTPInfo &TPInfo);


//ELANTPDLL_API BOOL RunService(/*char* pName*/);
//ELANTPDLL_API BOOL KillService(/*char* pName*/);

//
// Precise Touch having different mode after ic is power on. Here is a limitation before IC enter the RAW mode.
// And, for precise touch device, application needs to make sure the device is already in RAW mode before sending any command.
//
ELANTPDLL_API int __stdcall PreciseGetMode(PRECISE_TOUCH_MODE* nCurMode, int nTimeoutMS = -1, int nDevIdx = 0);

} // namespace ELANAPI



////////////////////////////////////////////////////////
// HP ezClikc 
////////////////////////////////////////////////////////
// Caro ++ 2023/07/24
//ELANTPDLL_API int __stdcall BRDG_GetFirmwareVer(unsigned int& rFWVer);
//ELANTPDLL_API int __stdcall BRDG_INTR_StateGet(unsigned char* pState);

//ELANTPDLL_API int __stdcall BRDG_BulkOutCmd(unsigned char* pszCommandBuf, int nCommandLen, int nTimeoutMS = -1, int nDevIdx = 0);
//ELANTPDLL_API int __stdcall BRDG_BulkInControl(unsigned char* pBuf, int iBufByteSz, int nTimeoutMS = -1, int nUSBDevIdx = 0);
//ELANTPDLL_API int __stdcall BRDG_BulkInData(unsigned char* pBuf, int iBufByteSz, int nTimeoutMS = -1, int nUSBDevIdx = 0);

ELANTPDLL_API int __stdcall EZCLICK_ProcessCreate(unsigned char* pBuf, int iBufByteSz, int nTimeoutMS = -1, int nDevIdx = 0);
ELANTPDLL_API int __stdcall EZCLICK_ProcessKill(unsigned char* pBuf, int iBufByteSz, int nTimeoutMS = -1, int nDevIdx = 0);
ELANTPDLL_API int __stdcall EZCLICK_AutoRunRegister(unsigned char* pBuf, int iBufByteSz, int nTimeoutMS = -1, int nUSBDevIdx = 0);
ELANTPDLL_API int __stdcall EZCLICK_AutoRunUnregister(unsigned char* pBuf, int iBufByteSz, int nTimeoutMS = -1, int nUSBDevIdx = 0);

// Caro --


#endif // _LIB_TOUCH_H_INCLUDE_948228FV_85JI_2964_IE94_058276KL4A52