// DemoAddTblParam.cpp : Defines the entry point for the console application.
//

#include "stdafx.h"
#include "Shlwapi.h"
#include "ErrCode.h"
#include "LibTouch.h"
#include "LibAddrTblParam.h"

void DebugShow(char* sDbgMessage, int strLen)
{
	printf_s("\r\n");
	printf_s(sDbgMessage);
}

int _tmain(int argc, _TCHAR* argv[])
{
	TCHAR szEXEPath[2048];
	TCHAR szUnicodeINIPath[2048];
	TCHAR szDrive[512], szDir[512], szFileName[512], szExt[512];
	int nRet = TP_SUCCESS;
	string		strAnsiInIFileName;

	ELANAPI::SetMsgOutFunc(NULL, DebugShow);

	GetModuleFileName ( NULL, szEXEPath, 2048 );
	_tsplitpath_s( szEXEPath, szDrive, szDir, szFileName, szExt);
	_stprintf_s(szUnicodeINIPath, 2048, _T("%s%sIQ_Table_Max2.ini"), szDrive, szDir);

	strAnsiInIFileName.assign(szUnicodeINIPath, strlen(szUnicodeINIPath));
	// If need to convert to AnsiFile, use it
	strAnsiInIFileName = strAnsiInIFileName.substr(0, strAnsiInIFileName.find(".")) + "_Ansi.ini";
	if (PathFileExists(strAnsiInIFileName.c_str()))
		DeleteFile(strAnsiInIFileName.c_str());

	nRet = ELANAPI::UnicodeFileToAnsiFile(szUnicodeINIPath, strAnsiInIFileName.c_str());
	if (nRet != TP_SUCCESS ){
		printf_s("Can't write Unicode File to Ansi File.\nPlease check it.\n");
		goto INI_READER_EXIT;
	}

	if (!ELANAPI::LoadFromInIFile(strAnsiInIFileName.c_str())){
		printf_s("Can't open INI file for Address Table.\nPlease check it.\n");
		goto INI_READER_EXIT;
	}

	ELANAPI::InIExportEktFile("IAP_INIFile.ekt");
	//ELANAPI::InIExportEktFileWithLastPage(const char* sEktFileName, unsigned char *pucLastPage);
	printf_s("Save Ekt file successfully!!!");

INI_READER_EXIT:

	if (nRet != TP_SUCCESS ){
		printf_s(ELANAPI::GetErrMessage());
	}

	_getch();
	return 0;
}

