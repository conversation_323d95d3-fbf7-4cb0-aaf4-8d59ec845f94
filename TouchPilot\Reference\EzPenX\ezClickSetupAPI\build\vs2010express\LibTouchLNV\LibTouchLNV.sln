﻿
Microsoft Visual Studio Solution File, Format Version 11.00
# Visual C++ Express 2010
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "LibTouch_Lenovo", "LibLenovo.vcxproj", "{6365C546-B9C0-4522-904E-D217AB5CFCE0}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "DemoActivePen", "..\..\..\bin\windows\Lenovo\windows_ActivePen\DemoActivePen.vcxproj", "{4E36B3B7-EEC5-4FCB-B1F0-68AB76D32AE5}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Win32 = Debug|Win32
		Debug|X64 = Debug|X64
		Release|Win32 = Release|Win32
		Release|X64 = Release|X64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6365C546-B9C0-4522-904E-D217AB5CFCE0}.Debug|Win32.ActiveCfg = Debug|Win32
		{6365C546-B9C0-4522-904E-D217AB5CFCE0}.Debug|Win32.Build.0 = Debug|Win32
		{6365C546-B9C0-4522-904E-D217AB5CFCE0}.Debug|X64.ActiveCfg = Debug|X64
		{6365C546-B9C0-4522-904E-D217AB5CFCE0}.Debug|X64.Build.0 = Debug|X64
		{6365C546-B9C0-4522-904E-D217AB5CFCE0}.Release|Win32.ActiveCfg = Release|Win32
		{6365C546-B9C0-4522-904E-D217AB5CFCE0}.Release|Win32.Build.0 = Release|Win32
		{6365C546-B9C0-4522-904E-D217AB5CFCE0}.Release|X64.ActiveCfg = Release|X64
		{6365C546-B9C0-4522-904E-D217AB5CFCE0}.Release|X64.Build.0 = Release|X64
		{4E36B3B7-EEC5-4FCB-B1F0-68AB76D32AE5}.Debug|Win32.ActiveCfg = Debug|Win32
		{4E36B3B7-EEC5-4FCB-B1F0-68AB76D32AE5}.Debug|Win32.Build.0 = Debug|Win32
		{4E36B3B7-EEC5-4FCB-B1F0-68AB76D32AE5}.Debug|X64.ActiveCfg = Debug|Win32
		{4E36B3B7-EEC5-4FCB-B1F0-68AB76D32AE5}.Release|Win32.ActiveCfg = Release|Win32
		{4E36B3B7-EEC5-4FCB-B1F0-68AB76D32AE5}.Release|Win32.Build.0 = Release|Win32
		{4E36B3B7-EEC5-4FCB-B1F0-68AB76D32AE5}.Release|X64.ActiveCfg = Release|Win32
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
