#ifndef _ERROR_CODE_H_A4729T84_3223_58276_5692_AV9FK38CMS9
#define _ERROR_CODE_H_A4729T84_3223_58276_5692_AV9FK38CMS9

//////////////////////////////////////////////////////////////
// Error code definition
//////////////////////////////////////////////////////////////

//** Function execution is success. **/
#ifndef TP_SUCCESS
#define TP_SUCCESS					0x0000	
#endif
/** This function is not support. **/
#ifndef TP_ERR_COMMAND_NOT_SUPPORT
#define TP_ERR_COMMAND_NOT_SUPPORT  0x0001
#endif
/** The touch ic may be occupied by other application **/
#ifndef TP_ERR_DEVICE_BUSY
#define TP_ERR_DEVICE_BUSY			0x0002
#endif
//J2++
/** Read/Write data timeout **/
#ifndef TP_ERR_TIMEOUT
#define TP_ERR_TIMEOUT				0x0003
#endif
//J2--
/** For asynchorous call, the execution is not finish yet. Waitting for complete **/
#ifndef TP_ERR_IO_PENDING
#define TP_ERR_IO_PENDING			0x0004
#endif
/** For any read command, sometimes the return data should meet some rule. **/
#ifndef TP_ERR_DATA_PATTEN
#define TP_ERR_DATA_PATTEN			0x0005
#endif

//J2++
/** No interface create **/
#ifndef TP_ERR_NO_INTERFACE_CREATE
#define TP_ERR_NO_INTERFACE_CREATE	0x0006
#endif
//J2--

#ifndef TP_ERR_HID_NOT_SUPPORT
#define TP_ERR_HID_NOT_SUPPORT		0x0007
#endif

/** Connect Elan Bridge and not get hello packet **/
#ifndef TP_ERR_CONNECT_NO_HELLO_PACKEY
#define TP_ERR_CONNECT_NO_HELLO_PACKEY		0x1002
#endif
/** Connect Elan Bridge and get recovery packet **/
#ifndef TP_ERR_RECOVERY_MODE
#define TP_ERR_RECOVERY_MODE				0x1003
#endif
/** Did not find any support device connectted to PC. Please check connectoin. **/
#ifndef TP_ERR_NOT_FOUND_DEVICE
#define TP_ERR_NOT_FOUND_DEVICE				0x1004
#endif

/** Test Mode Error Code **/
#ifndef TP_TESTMODE_GET_RAWDATA_FAIL
#define TP_TESTMODE_GET_RAWDATA_FAIL		0x3001			
#endif 

/** CSV Parameter Error Code **/
#ifndef ERR_CSVFILE_NOT_EXIST
#define ERR_CSVFILE_NOT_EXIST				0x4001
#endif

#ifndef ERR_CSVFILE_OPEN_FAILURE
#define ERR_CSVFILE_OPEN_FAILURE			0x4002
#endif

#ifndef ERR_PARAM_FILE_INFO_INVALID
#define ERR_PARAM_FILE_INFO_INVALID			0x4003
#endif

#ifndef ERR_INIFILE_OPEN_FAILURE
#define ERR_INIFILE_OPEN_FAILURE			0x5001
#endif

#ifndef ERR_EEPROM_WRITE_FAILURE
#define ERR_EEPROM_WRITE_FAILURE			0x6001
#endif

/** Error information check, use GetErrMsg to get the error message. **/
#ifndef TP_ERR_CHK_MSG
#define TP_ERR_CHK_MSG						0xFFFF
#endif 

#ifndef ERR_LOAD_USB_LIBRARY
#define ERR_LOAD_USB_LIBRARY				0x7001
#endif

#ifndef ERR_NONE_USB_DEVICE
#define ERR_NONE_USB_DEVICE					0x7002
#endif

#ifndef ERR_ALLOC_MEM
#define ERR_ALLOC_MEM						0x7003
#endif

#ifndef ERR_GET_DEV_CONFIGURATION
#define ERR_GET_DEV_CONFIGURATION			0x7004
#endif

#ifndef ERR_GET_DEV_INTERFACE
#define ERR_GET_DEV_INTERFACE				0x7005
#endif

#ifndef ERR_OPEN_BULK_DEVICE
#define ERR_OPEN_BULK_DEVICE				0x7006
#endif

#ifndef ERR_BULKOUT_FAILURE					
#define ERR_BULKOUT_FAILURE					0x7007
#endif

#ifndef ERR_BULKIN_FAILURE
#define ERR_BULKIN_FAILURE					0x7008
#endif

#ifndef ERR_BULKIN_TIMEOUT_FAILURE
#define ERR_BULKIN_TIMEOUT_FAILURE			0x7009
#endif
#endif //_ERROR_CODE_H_A4729T84_3223_58276_5692_AV9FK38CMS9