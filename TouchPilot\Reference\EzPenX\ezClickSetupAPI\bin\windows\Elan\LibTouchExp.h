#ifndef _INCLUDE_LIB_TOUCH_EXP_H_9Y256A_9B578BC3_9836_682C
#define _INCLUDE_LIB_TOUCH_EXP_H_9Y256A_9B578BC3_9836_682C

#ifdef ELANTPDLL_EXPORTS
#define ELANTPDLL_API extern "C" __declspec(dllexport)
#else
#define ELANTPDLL_API extern "C" __declspec(dllimport)
#endif
//
//#include <windows.h>
//
//extern "C" {
//#include "Hidsdi.h"
//#include "SetupAPI.h"
//#include "HidUsage.h"
//#include "HidPi.h"
//}
////////////////////////////////////////////////////////////
//Declare the constant value
////////////////////////////////////////////////////////////

#ifndef MAX_TOUCH_REPORT_LEN
#define MAX_TOUCH_REPORT_LEN	116		// The maximum number of each HID report 
#endif
#ifndef MAX_TOUCH_REPORT_NUM
#define MAX_TOUCH_REPORT_NUM	100		// The maximum number of storing finger report
#endif
#ifndef MAX_LEN
#define MAX_LEN					256		// 3900x3 Max Rx Length //82; //3900x2 Max Rx Length //Max XTrace and YTrace Number
#endif
#ifndef MAX_CHIP_NUM
#define MAX_CHIP_NUM			4		// Max IC Number
#endif

//J2 20180131++
#ifndef MAX_PROF_NUM
#define MAX_PROF_NUM			4		// Max Profile Number
#endif
//J2--


////////////////////////////////////////////////////////////
// Connect 
////////////////////////////////////////////////////////////
enum TP_INTERFACE_TYPE{ TP_IF_USB = 0 , TP_IF_HIDI2C, TP_IF_I2C, TP_IF_SPI_RISE_HALF, TP_IF_SPI_FALL_HALF, TP_IF_SPI_RISE, TP_IF_SPI_FALL, TP_IF_SPI_PRECISE };
enum PRECISE_TOUCH_MODE{ PT_UNKNOW_MODE, PT_RAW_MODE , PT_RECOVERY_IAP_MODE, PT_HID_MODE };

//J2++
//A structure that describe the Trace information
typedef struct _TRACE_INFORMATION
{
	int nChipNum;
	int nXTotal;
	int nYTotal;
	int XAxis[MAX_CHIP_NUM];
	int nPartialNum;
} TraceInfo;
//J2--

class __declspec(dllexport) CTPInfo
{
public:
	CTPInfo();
	int GetXTrace(void);
	int GetYTrace(void);
	void GetTraceInfo(TraceInfo* pTraceInfo);
	void SetTraceInfo(TraceInfo* pTraceInfo);

	int RAWData[18/*PROTOCAL_REPORT_LEN_TPINFO*/];
	int nChipNum;
	int nXTotal;
	int nYTotal;
	int XAxis[MAX_CHIP_NUM];
	int nPartialNum;
	int nFWID;
	int nFWVersion;
	int nTestVersion;
	int nSolutionVersion;
	int nBootCodeVersion;
	int nWHCKVersion;
	//J2++
	int nSNVersion;
	//J2--

	// Alan 20220524
	int nGen8TestVersion;
	int nGen8CodeBaseVersion;
	int nGen8ParameterValue;
	int nGen8ParameterDef;
	//~Alan 20220524

	// For Pen Raw Data in split data 
	int nRawChannelTotalNum;
	int nRawChannelDepth;
	int nRawChannelNum;	// Firmware return fewer Col data, not all data. Call "Channel Count"
};

////////////////////////////////////////////////////////////
// Profile Header Content Tag
////////////////////////////////////////////////////////////
#define EMC_PROFILE_TOUCH				0x0001
#define EMC_PROFILE_REG_RAW_INPUT		0x0002

# pragma pack (1)

typedef struct _PROFILE_HEADER {
  unsigned long      Length;
  unsigned long      ContentTag;
}PROFILE_HEADER, *PPROFILE_HEADER;
//
// HID device "Active Pen" information
// 
typedef struct _EMC_VALUE_CAPS
{
	bool bSupport;
	bool bReserved[3];

	int iLocialMax;
	int	iDataIdx;
	int iUsage;
	int iUsagePage;
	int iReportCnt;
	int iBitSz;
}EMC_VALUE_CAPS, *PEMC_VALUE_CAPS;

typedef struct _PEN_PROFILE
{
	int ReportID;
	
	// button indicator index
	int TipIdx;
	int TipUsage;
	int TipUsagePage;

	int BarrelSwitchIdx;
	int BarrelSwitchUsage;
	int BarrelSwitchUsagePage;

	int EraserIdx;
	int EraserUsage;
	int EraserUsagePage;

	int InvertIdx;
	int InvertUsage;
	int InvertUsagePage;

	int InRangeIdx;
	int InRangeUsage;
	int InRangeUsagePage;

	EMC_VALUE_CAPS	CapsLogicalX;
	EMC_VALUE_CAPS	CapsLogicalY;
	EMC_VALUE_CAPS	CapsTipPressure;
	EMC_VALUE_CAPS	CapsBatteryStrength;
	EMC_VALUE_CAPS	CapsVendor;
	EMC_VALUE_CAPS	CapsReportTest;
	EMC_VALUE_CAPS  CapsTiltX;		// The range between the Y-Z planeand the pointer device plane. Positive is toward the user right hand.
	EMC_VALUE_CAPS  CapsTiltY;      // The range between the X-Z planeand the pointer device plane. Positive is toward the user.
	EMC_VALUE_CAPS  CapsAzimuth;	// The counter-clockwise rotation of the cursor about the Z-axis.

	int				inputReportLength;	// Pen Report Length

}PEN_PROFILE, *PPEN_PROFILE;

//
// HID device "Finger" information
// 
typedef struct _FINGER_PROFILE
{
	//m_nContactNumIdx = m_nContactMaxNumEachReport * m_nContactDataSize + m_nScanTimeSize + 1;	
	int nContactNumIdx;
	//
	// HID device information
	// 
	int ReportID;
	int maxLogicalX;
	int maxLogicalY;	
	int maxContactCnt;					// 10 fingers max. 
	int inputReportLength;				// Touch report length is 116 bytes.

	int nScanTimeByteNum;				// Take 4 bytes space in every touch report
	int nContactDataByteNum;			// Take 11 byte for every contacted finger data
	int nContactMaxNumEachReport;		// 2 for hybrid mode.. Calculate - every input report carray 5 contact finger report

	int byteSizeLogicalX;
	int byteSizeScanTime;
	int byteSizeContactWidth;

	int idxTip;
	int idxX;
	int idxY;
	int idxContactID;
	int idxContactHeight;
	int idxContactWidth;	
	int idxScanTime;
	int idxCurContactCnt;

	//J2++
	//Declare the variable to store the input caps.
	//These caps use to parse the finger report data.
	EMC_VALUE_CAPS	CapsContactID;
	EMC_VALUE_CAPS	CapsWidth;
	EMC_VALUE_CAPS	CapsHeight;
	EMC_VALUE_CAPS	CapsLogicalX;
	EMC_VALUE_CAPS	CapsLogicalY;
	EMC_VALUE_CAPS	CapsScanTime;
	EMC_VALUE_CAPS	CapsActualCount;
	//J2--
}FINGER_PROFILE, *PFINGER_PROFILE;

//
// HID device "Finger" information
// 
typedef struct _LINETEST_PROFILE
{
	//
	// HID device information
	// 
	int ReportID;
	int inputReportLength;				// Line Test reprot length is 32 bytes.
	EMC_VALUE_CAPS CapsLineTest;

}LINETEST_PROFILE, *PLINETEST_PROFILE;

// EMC_PROFILE_TOUCH
typedef struct _TOUCH_PROFILE_
{
	PROFILE_HEADER	pProfileHeader;

	int				nDevIdx;				
	bool			bSupportFinger;
	bool			bSupportPen;
	bool			bSupportLineTest;
	//J2++
	bool			bIsPreciseTouch;
	//J2--
	bool			bSupportPenFeature;

	//J2 modified 20180131++
	//Due to multi-finger profile declare in descriptor.
	//Declare the array to store profile.
	int					ProfFingerNum;
	FINGER_PROFILE		ProfFinger[MAX_PROF_NUM];
	//J2--
	PEN_PROFILE			ProfPen;
	LINETEST_PROFILE	ProfLineTest;
}TOUCH_PROFILE, *PTOUCH_PROFILE;

// EMC_PROFILE_REG_RAW_INPUT
typedef struct _RAWINPUT_PROFILE
{
	PROFILE_HEADER	pProfileHeader;
}RAWINPUT_PROFILE, *PRAWINPUT_PROFILE;

# pragma pack ()

#endif