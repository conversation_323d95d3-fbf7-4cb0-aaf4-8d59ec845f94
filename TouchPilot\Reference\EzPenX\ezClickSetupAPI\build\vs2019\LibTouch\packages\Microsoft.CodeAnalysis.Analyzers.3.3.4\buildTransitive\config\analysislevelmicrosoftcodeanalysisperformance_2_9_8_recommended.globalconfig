# NOTE: Requires **VS2019 16.7** or later

# 'MicrosoftCodeAnalysisPerformance' Rules from '2.9.8' release with 'Recommended' analysis mode
# Description: 'MicrosoftCodeAnalysisPerformance' Rules with enabled-by-default state from '2.9.8' release with 'Recommended' analysis mode. Rules that are first released in a version later than '2.9.8' are disabled.

is_global = true

global_level = -99


# RS1034: Prefer 'IsKind' for checking syntax kinds
dotnet_diagnostic.RS1034.severity = none
