# NOTE: Requires **VS2019 16.7** or later

# 'MicrosoftCodeAnalysisDesign' Rules from '3.3.3' release with 'Default' analysis mode
# Description: 'MicrosoftCodeAnalysisDesign' Rules with enabled-by-default state from '3.3.3' release with 'Default' analysis mode. Rules that are first released in a version later than '3.3.3' are disabled.

is_global = true

global_level = -99


# RS1037: Add "CompilationEnd" custom tag to compilation end diagnostic descriptor
dotnet_diagnostic.RS1037.severity = none
