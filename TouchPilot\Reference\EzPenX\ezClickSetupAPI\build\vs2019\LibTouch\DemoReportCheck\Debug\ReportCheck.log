﻿Build started 2023/8/11 下午 04:38:46.
     1>Project "D:\workspace\Git_ScriptAgent\ScriptAgent\LibTouch\build\vs2010express\LibTouch\DemoReportCheck\ReportCheck.vcxproj" on node 2 (build target(s)).
     1>InitializeBuildStatus:
         Creating "Debug\ReportCheck.unsuccessfulbuild" because "AlwaysCreate" was specified.
       ClCompile:
         All outputs are up-to-date.
       ResourceCompile:
         All outputs are up-to-date.
       ManifestResourceCompile:
         All outputs are up-to-date.
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:"D:\workspace\Git_ScriptAgent\ScriptAgent\LibTouch\build\vs2010express\LibTouch\Debug\ReportCheck.exe" /INCREMENTAL /NOLOGO /LIBPATH:..\..\..\..\bin\windows\Elan LibTouchD.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib Comctl32.lib kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /ManifestFile:"Debug\ReportCheck.exe.intermediate.manifest" /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /DEBUG /PDB:"D:\workspace\Git_ScriptAgent\ScriptAgent\LibTouch\build\vs2010express\LibTouch\Debug\ReportCheck.pdb" /SUBSYSTEM:WINDOWS /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:\workspace\Git_ScriptAgent\ScriptAgent\LibTouch\build\vs2010express\LibTouch\Debug\ReportCheck.lib" /MACHINE:X86 Debug\ReportCheck.res
         Debug\ReportCheck.exe.embed.manifest.res
         Debug\BulkSPICtrlTest.obj
         Debug\ReportCheck.obj
         Debug\stdafx.obj
            Creating library D:\workspace\Git_ScriptAgent\ScriptAgent\LibTouch\build\vs2010express\LibTouch\Debug\ReportCheck.lib and object D:\workspace\Git_ScriptAgent\ScriptAgent\LibTouch\build\vs2010express\LibTouch\Debug\ReportCheck.exp
       Manifest:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /out:"Debug\ReportCheck.exe.embed.manifest" /manifest Debug\ReportCheck.exe.intermediate.manifest
         All outputs are up-to-date.
       LinkEmbedManifest:
         All outputs are up-to-date.
         ReportCheck.vcxproj -> D:\workspace\Git_ScriptAgent\ScriptAgent\LibTouch\build\vs2010express\LibTouch\Debug\ReportCheck.exe
       FinalizeBuildStatus:
         Deleting file "Debug\ReportCheck.unsuccessfulbuild".
         Touching "Debug\ReportCheck.lastbuildstate".
     1>Done Building Project "D:\workspace\Git_ScriptAgent\ScriptAgent\LibTouch\build\vs2010express\LibTouch\DemoReportCheck\ReportCheck.vcxproj" (build target(s)).

Build succeeded.

Time Elapsed 00:00:00.44
