# NOTE: Requires **VS2019 16.7** or later

# 'MicrosoftCodeAnalysisDesign' Rules from '3.0.0' release with 'Recommended' analysis mode escalated to 'error' severity
# Description: 'MicrosoftCodeAnalysisDesign' Rules with enabled-by-default state from '3.0.0' release with 'Recommended' analysis mode. Rules that are first released in a version later than '3.0.0' are disabled. Enabled rules with 'warning' severity are escalated to 'error' severity to respect 'CodeAnalysisTreatWarningsAsErrors' MSBuild property.

is_global = true

global_level = -99


# RS1017: DiagnosticId for analyzers must be a non-null constant
dotnet_diagnostic.RS1017.severity = error

# RS1018: DiagnosticId for analyzers must be in specified format
dotnet_diagnostic.RS1018.severity = error

# RS1019: DiagnosticId must be unique across analyzers
dotnet_diagnostic.RS1019.severity = error

# RS1021: Invalid entry in analyzer category and diagnostic ID range specification file
dotnet_diagnostic.RS1021.severity = error

# RS1029: Do not use reserved diagnostic IDs
dotnet_diagnostic.RS1029.severity = error

# RS1031: Define diagnostic title correctly
dotnet_diagnostic.RS1031.severity = none

# RS1032: Define diagnostic message correctly
dotnet_diagnostic.RS1032.severity = none

# RS1033: Define diagnostic description correctly
dotnet_diagnostic.RS1033.severity = none

# RS1037: Add "CompilationEnd" custom tag to compilation end diagnostic descriptor
dotnet_diagnostic.RS1037.severity = none
