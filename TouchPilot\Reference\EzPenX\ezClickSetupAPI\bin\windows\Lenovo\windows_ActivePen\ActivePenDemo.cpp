// ActivePenDemo.cpp : Defines the entry point for the application.
//

#include "stdafx.h"
#include "ActivePenDemo.h"
#include <windows.h>
#include <stdlib.h>
#include <wtypes.h>
#include <dbt.h>
#include "LibTouchLNV.h"

//****************************************************************************
// Global module variables
//****************************************************************************
static HINSTANCE          hGInstance; //global application instance handle

static HWND				  g_hGOutWinCtrl = NULL;
					
CRITICAL_SECTION			g_csReportAccess;
EXPORT_TOUCH_PROFILE		g_TouchProfile;
//****************************************************************************
// UI FUNCTIONS
//****************************************************************************
INT_PTR CALLBACK bMainDlgProc( HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam);

//****************************************************************************
// TOUCH DEVIcE FUNCTIONS
//****************************************************************************
void ConnectDevice(HWND hWnd);
void __stdcall ReportReceive(unsigned char* pReportBuf, int nReportLen, int nReportCount, SYSDELTA_TIME timeStamp);
//****************************************************************************
// Windows message handler function
//****************************************************************************
void MSGCommandHandler(HWND hDlg, WPARAM wParam, LPARAM lParam);

/*******************************
*WinMain: Windows Entry point  *
*******************************/
int APIENTRY _tWinMain(HINSTANCE hInstance,
                     HINSTANCE hPrevInstance,
                     LPTSTR    lpCmdLine,
                     int       nCmdShow)
{
	UNREFERENCED_PARAMETER(hPrevInstance);
    UNREFERENCED_PARAMETER(lpCmdLine);
    UNREFERENCED_PARAMETER(nCmdShow);

    LPWSTR  *szArgList = NULL;

    //
    // Save instance of the application for further reference
    //
    hGInstance = hInstance;

	if (-1 == DialogBox(hInstance, _T("MAIN_DIALOG"), NULL, bMainDlgProc)) {
		MessageBox(NULL, _T("Unable to create root dialog!"), _T("DialogBox failure"), MB_ICONSTOP);
	}

    if (NULL != szArgList)
    {
        LocalFree(szArgList);
        szArgList = NULL;
    }

    return (0);
}

/*************************************************
 * Main Dialog proc                              *
 *************************************************/
INT_PTR CALLBACK 
bMainDlgProc(
    HWND hDlg, 
    UINT message, 
    WPARAM wParam, 
    LPARAM lParam
)
{
    BOOL                             status = FALSE;

    switch (message)
    {
        case WM_INITDIALOG:
			break;	// end WM_INITDIALOG case
		case WM_SHOWWINDOW:
			InitializeCriticalSectionAndSpinCount(&g_csReportAccess, 0x8000);
			g_hGOutWinCtrl = GetDlgItem(hDlg,IDC_ITEMS);

			ConnectDevice(hDlg);
            break; 

        case WM_COMMAND:
			MSGCommandHandler(hDlg, wParam, lParam);
            break;

		break;
   } // end switch message
   return FALSE;
} // end MainDlgProc

/////////////////////////////////////////////////////////////////////////
//
//
void DebugShow(char* sDbgMessage, int strLen)
{	
    int	iIndex;

	if ( g_hGOutWinCtrl != NULL ) {
		iIndex = (INT) SendMessage(g_hGOutWinCtrl, LB_ADDSTRING, 0, (LPARAM) sDbgMessage);

	}

}

/////////////////////////////////////////////////////////////////////////
//
//
void ResetShow()
{	
    SendMessage(g_hGOutWinCtrl, LB_RESETCONTENT, 0, (LPARAM) 0);
}

/////////////////////////////////////////////////////////////////////////
//
//
void ConnectDevice(HWND hWnd)
{
	CHAR	sTmp[512];
	int nRet = TP_SUCCESS;
	int nDevCnt = 0;

	unsigned int nFWID = 0;
	unsigned int nFWVer = 0;
	unsigned int nVID = 0;
	unsigned int nPID = 0;

	sprintf_s(sTmp, "Try to connect device.");
	DebugShow(sTmp, strlen(sTmp));


	nRet = ELANAPI::Connect(0x04F3, 0x00);
	if ( nRet != TP_SUCCESS ) {
		sprintf_s(sTmp, " -->%s", ELANAPI::GetErrMessage());
		DebugShow(sTmp, strlen(sTmp));
		goto Connect_Device_Exit;
	}

	sprintf_s(sTmp, " --> Success.");
	DebugShow(sTmp, strlen(sTmp));


	nRet = ELANAPI::GetProfileExport(&g_TouchProfile);
	if ( nRet != TP_SUCCESS ) {
		goto Connect_Device_Exit;
	}
	
	if ( g_TouchProfile.bSupportPen == true ) {
		
		sprintf_s(sTmp, "\r\nActive Pen Report ID = %d:", g_TouchProfile.ProfPen.ReportID);
		DebugShow(sTmp, strlen(sTmp));

		sprintf_s(sTmp, "\r\nMax.X = %d Max.Y=%d", 
			g_TouchProfile.ProfPen.CapsLogicalX.iLocialMax, g_TouchProfile.ProfPen.CapsLogicalY.iLocialMax);
		DebugShow(sTmp, strlen(sTmp));

		sprintf_s(sTmp, "\r\nMax.Pressure = %d", g_TouchProfile.ProfPen.CapsTipPressure.iLocialMax);
		DebugShow(sTmp, strlen(sTmp));		
	}

	if ( g_TouchProfile.bSupportFinger == true ) {
		sprintf_s(sTmp, "Finger Report ID = %d:", g_TouchProfile.ProfFinger.ReportID);
		DebugShow(sTmp, strlen(sTmp));

		sprintf_s(sTmp, "Max.X = %d Max.Y=%d", 
			g_TouchProfile.ProfFinger.maxLogicalX, g_TouchProfile.ProfFinger.maxLogicalY);
		DebugShow(sTmp, strlen(sTmp));	
	}

	nRet = ELANAPI::InputRawRegHIDCallback(ReportReceive); 
	if ( nRet != TP_SUCCESS ) {
		goto Connect_Device_Exit;
	}

Connect_Device_Exit:
	if ( nRet != TP_SUCCESS ) {
		sprintf_s(sTmp, "\r\nError : %s",  ELANAPI::GetErrMessage() );
		DebugShow(sTmp, strlen(sTmp));
	}
	return ;
}

/////////////////////////////////////////////////////////////////////////
//
//
void __stdcall ReportReceive(unsigned char* pReportBuf, int nReportLen, int nReportCount, SYSDELTA_TIME timeStamp)
{
	CHAR sOut[512] = {'\0'};
	CHAR sTmp[512] = {'\0'};
	int cnt = 0;
	int i = 0;
	int j = 0;

	sprintf_s(sOut, "%02d:%02d:%02d %03d %06d", timeStamp.wHour , timeStamp.wMinute, timeStamp.wSecond, timeStamp.wMilliseconds, timeStamp.llDeltaMicroSeconds);
	DebugShow(sOut, strlen(sOut));

	int nRet = TP_SUCCESS;
	int nPacketCnt = 0;
	char szResult[1024] = {0};
	unsigned char* pRpData = NULL;

	EnterCriticalSection(&g_csReportAccess); 

	for(int i = 0; i < nReportCount; i++) {
		
		pRpData = & pReportBuf[nReportLen * i];

		if (( pRpData[0] == g_TouchProfile.ProfPen.ReportID ) && ( g_TouchProfile.bSupportPen ) ) {

			PTOUCH_PEN_DATA report = (PTOUCH_PEN_DATA)(pRpData);

			ResetShow();

			sprintf_s(szResult, "bInRange = %d, bTip = %d, bBarrel = %d \r\n",
						report->bInRange,  report->bTip, report->bBarrel );	

			DebugShow(szResult, strlen(szResult));

			sprintf_s(szResult, "bInvert = %d, bErase = %d \r\n", report->bInvert, report->bErase);
		
			DebugShow(szResult, strlen(szResult));

			sprintf_s(szResult, "x = %d, y = %d, prssure =%d",
						((report->x_position_msbyte << 8 &0xFF00) | report->x_position_lsbyte),
						((report->y_position_msbyte << 8 &0xFF00) | report->y_position_lsbyte),
						((report ->tip_pressure_msbyte << 8 &0xFF00) | report->tip_pressure_lsbyte) );
		
			DebugShow(szResult, strlen(szResult));
		}
		else if (( pRpData[0] == g_TouchProfile.ProfFinger.ReportID ) && ( g_TouchProfile.bSupportFinger ) ) {

			PTOUCH_FINGER_DATA finger_report = (PTOUCH_FINGER_DATA)(pRpData);

			ResetShow();

			sprintf_s(szResult, "Active finger number : %d, \r\n", finger_report->active_touch_count );	
			DebugShow(szResult, strlen(szResult));

			for (int idx = 0; idx < finger_report->active_touch_count; idx++) {
				
				sprintf_s(szResult, "\r\n[finger_%02d]ID = %d, bTip = %d, width = %d, height = %d, X = %d, Y = %d", idx,
					finger_report->finger[idx].contactID,  finger_report->finger[idx].bTip,
					finger_report->finger[idx].contact_width, finger_report->finger[idx].contact_height, 
					((finger_report->finger[idx].x_position_msbyte << 8 &0xFF00) | finger_report->finger[idx].x_position_lsbyte),
					((finger_report->finger[idx].y_position_msbyte << 8 &0xFF00) | finger_report->finger[idx].y_position_lsbyte));
			
				DebugShow(szResult, strlen(szResult));
			}
		}
	}

//Report_Receive_Exit:

	LeaveCriticalSection(&g_csReportAccess); 

	return;
}
/////////////////////////////////////////////////////////////////////////
//
//
void MSGCommandHandler(HWND hDlg, WPARAM wParam, LPARAM lParam)
{
	int nRet = TP_SUCCESS;
	unsigned char cmd[] = {0x53, 0xF0, 0x00, 0x01}; // Read FW ID Command
	unsigned char Buf[20];
	char szOut[512] = {'\0'};
	char szTmp[128] = {'\0'};
	int i = 0;
	switch(LOWORD(wParam))
	{                                          
	case IDC_ABOUT:

		MessageBox(hDlg, _T("Sample Active Pen demo Application. ELAN Corp \nCopyright (C) 2015"),
						_T("About Active Pen Demo"), MB_ICONINFORMATION);
		break;
	case IDC_EXECUTE:

		nRet = ELANAPI::SendDevCommand(cmd, sizeof(cmd));
		if ( nRet != TP_SUCCESS ) {
			break;
		}
		
		sprintf_s(szOut, "[Command]");
		for ( i = 0; i <  4 ; i ++ ) {
			sprintf_s(szTmp, " %02X", cmd[i]);
			strcat( szOut, szTmp);
		}
		DebugShow(szOut, strlen(szOut));


		memset(Buf, 0 , 20);
		nRet = ELANAPI::ReadDevData(Buf, 20);
		if ( nRet != TP_SUCCESS ) {
			break;
		}

		sprintf_s(szOut, "[  Data ]");
		for ( i = 0; i < 20 ; i ++ ) {
			sprintf_s(szTmp, " %02X", Buf[i]);
			strcat( szOut, szTmp);
		}
		DebugShow(szOut, strlen(szOut));

		break;
	case IDOK:
    case IDCANCEL:
		ELANAPI::Disconnect();
		EndDialog(hDlg,0);
		
		break;

	} //end switch wParam//
}
