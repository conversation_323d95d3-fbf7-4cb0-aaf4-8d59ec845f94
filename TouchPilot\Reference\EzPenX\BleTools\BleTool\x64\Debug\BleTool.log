﻿  BleTool.cpp
D:\workspace\Driver\PenX\src\PenSettingBeta\BleTools\BleTool\BleTool.cpp(994,9): error C3861: 'BluetoothStringToAddress': identifier not found
D:\workspace\Driver\PenX\src\PenSettingBeta\BleTools\BleTool\BleTool.cpp(1011,8): error C2660: 'BluetoothGetDeviceInfo': function does not take 3 arguments
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\bluetoothapis.h(538,1): message : see declaration of 'BluetoothGetDeviceInfo'
D:\workspace\Driver\PenX\src\PenSettingBeta\BleTools\BleTool\BleTool.cpp(1018,22): error C3861: 'BluetoothOpenDevice': identifier not found
D:\workspace\Driver\PenX\src\PenSettingBeta\BleTools\BleTool\BleTool.cpp(1031,5): error C2065: 'BLUETOOTH_DEVICE_SEARCH_RESULT': undeclared identifier
D:\workspace\Driver\PenX\src\PenSettingBeta\BleTools\BleTool\BleTool.cpp(1031,36): error C2146: syntax error: missing ';' before identifier 'searchResult'
D:\workspace\Driver\PenX\src\PenSettingBeta\BleTools\BleTool\BleTool.cpp(1031,36): error C2065: 'searchResult': undeclared identifier
D:\workspace\Driver\PenX\src\PenSettingBeta\BleTools\BleTool\BleTool.cpp(1031,60): error C2065: 'BLUETOOTH_DEVICE_SEARCH_RESULT': undeclared identifier
D:\workspace\Driver\PenX\src\PenSettingBeta\BleTools\BleTool\BleTool.cpp(1031,92): error C3079: an initializer list cannot be used as the right operand of this assignment operator
D:\workspace\Driver\PenX\src\PenSettingBeta\BleTools\BleTool\BleTool.cpp(1032,5): error C2065: 'searchResult': undeclared identifier
D:\workspace\Driver\PenX\src\PenSettingBeta\BleTools\BleTool\BleTool.cpp(1034,69): error C2065: 'searchResult': undeclared identifier
D:\workspace\Driver\PenX\src\PenSettingBeta\BleTools\BleTool\BleTool.cpp(1034,9): error C3861: 'BluetoothPerformInquiry': identifier not found
D:\workspace\Driver\PenX\src\PenSettingBeta\BleTools\BleTool\BleTool.cpp(1036,9): error C3861: 'BluetoothCloseDevice': identifier not found
D:\workspace\Driver\PenX\src\PenSettingBeta\BleTools\BleTool\BleTool.cpp(1041,30): error C2065: 'searchResult': undeclared identifier
D:\workspace\Driver\PenX\src\PenSettingBeta\BleTools\BleTool\BleTool.cpp(1041,12): warning C4473: 'printf' : not enough arguments passed for format string
D:\workspace\Driver\PenX\src\PenSettingBeta\BleTools\BleTool\BleTool.cpp(1041,12): message : placeholders and their parameters expect 1 variadic arguments, but 0 were provided
D:\workspace\Driver\PenX\src\PenSettingBeta\BleTools\BleTool\BleTool.cpp(1041,12): message : the missing variadic argument 1 is required by format string '%d'
D:\workspace\Driver\PenX\src\PenSettingBeta\BleTools\BleTool\BleTool.cpp(1044,5): error C3861: 'BluetoothCloseDevice': identifier not found
