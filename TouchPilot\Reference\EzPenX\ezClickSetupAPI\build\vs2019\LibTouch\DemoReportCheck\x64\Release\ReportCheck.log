﻿  BulkSPICtrlTest.cpp
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\BulkSPICtrlTest.cpp(416,18): error C2039: 'BRDG_SPIPureMOSIDataSet': is not a member of 'ELANAPI'
D:\workspace\Driver\PenX\src\LibClick\LibTouch\bin\windows\Elan\LibTouch.h(138): message : see declaration of 'ELANAPI'
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\BulkSPICtrlTest.cpp(416,41): error C3861: 'BRDG_SPIPureMOSIDataSet': identifier not found
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\BulkSPICtrlTest.cpp(423,18): error C2039: 'BRDG_SPIPureExecute': is not a member of 'ELANAPI'
D:\workspace\Driver\PenX\src\LibClick\LibTouch\bin\windows\Elan\LibTouch.h(138): message : see declaration of 'ELANAPI'
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\BulkSPICtrlTest.cpp(423,37): error C3861: 'BRDG_SPIPureExecute': identifier not found
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\BulkSPICtrlTest.cpp(463,18): error C2039: 'BRDG_SPIPureMOSIDataSet': is not a member of 'ELANAPI'
D:\workspace\Driver\PenX\src\LibClick\LibTouch\bin\windows\Elan\LibTouch.h(138): message : see declaration of 'ELANAPI'
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\BulkSPICtrlTest.cpp(463,41): error C3861: 'BRDG_SPIPureMOSIDataSet': identifier not found
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\BulkSPICtrlTest.cpp(472,18): error C2039: 'BRDG_SPIPureExecute': is not a member of 'ELANAPI'
D:\workspace\Driver\PenX\src\LibClick\LibTouch\bin\windows\Elan\LibTouch.h(138): message : see declaration of 'ELANAPI'
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\BulkSPICtrlTest.cpp(472,37): error C3861: 'BRDG_SPIPureExecute': identifier not found
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\BulkSPICtrlTest.cpp(482,18): error C2039: 'BRDG_SPIPureMISODataGet': is not a member of 'ELANAPI'
D:\workspace\Driver\PenX\src\LibClick\LibTouch\bin\windows\Elan\LibTouch.h(138): message : see declaration of 'ELANAPI'
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\BulkSPICtrlTest.cpp(482,41): error C3861: 'BRDG_SPIPureMISODataGet': identifier not found
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\BulkSPICtrlTest.cpp(495,18): error C2039: 'BRDG_SPIPureMOSIDataSet': is not a member of 'ELANAPI'
D:\workspace\Driver\PenX\src\LibClick\LibTouch\bin\windows\Elan\LibTouch.h(138): message : see declaration of 'ELANAPI'
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\BulkSPICtrlTest.cpp(495,41): error C3861: 'BRDG_SPIPureMOSIDataSet': identifier not found
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\BulkSPICtrlTest.cpp(502,18): error C2039: 'BRDG_SPIPureExecute': is not a member of 'ELANAPI'
D:\workspace\Driver\PenX\src\LibClick\LibTouch\bin\windows\Elan\LibTouch.h(138): message : see declaration of 'ELANAPI'
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\BulkSPICtrlTest.cpp(502,37): error C3861: 'BRDG_SPIPureExecute': identifier not found
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\BulkSPICtrlTest.cpp(507,18): error C2039: 'BRDG_SPIPureMISODataGet': is not a member of 'ELANAPI'
D:\workspace\Driver\PenX\src\LibClick\LibTouch\bin\windows\Elan\LibTouch.h(138): message : see declaration of 'ELANAPI'
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\BulkSPICtrlTest.cpp(507,41): error C3861: 'BRDG_SPIPureMISODataGet': identifier not found
  ReportCheck.cpp
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(211,25): warning C4312: 'type cast': conversion from 'int' to 'HMENU' of greater size
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(285,34): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(296,32): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(320,21): warning C4244: '=': conversion from 'LRESULT' to 'INT', possible loss of data
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(472,22): warning C4244: '=': conversion from 'LRESULT' to 'int', possible loss of data
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(480,80): warning C4554: '>>': check operator precedence for possible error; use parentheses to clarify precedence
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(481,79): warning C4554: '>>': check operator precedence for possible error; use parentheses to clarify precedence
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(697,3): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(730,18): warning C4477: 'sprintf_s' : format string '%06d' requires an argument of type 'int', but variadic argument 5 has type 'LONGLONG'
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(730,18): message : consider using '%lld' in the format string
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(730,18): message : consider using '%Id' in the format string
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(730,18): message : consider using '%I64d' in the format string
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(788,21): warning C4244: '=': conversion from 'UINT_PTR' to 'UINT', possible loss of data
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(1495,13): warning C4244: 'initializing': conversion from 'LRESULT' to 'int', possible loss of data
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(1519,25): warning C4244: '=': conversion from 'LRESULT' to 'int', possible loss of data
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(1524,68): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(1674,50): warning C4474: 'sprintf_s' : too many arguments passed for format string
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(1674,50): message : placeholders and their parameters expect 0 variadic arguments, but 1 were provided
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(1709,51): warning C4474: 'sprintf_s' : too many arguments passed for format string
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(1709,51): message : placeholders and their parameters expect 0 variadic arguments, but 1 were provided
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(1747,55): warning C4474: 'sprintf_s' : too many arguments passed for format string
D:\workspace\Driver\PenX\src\LibClick\LibTouch\demo\windows_ReportCheck\ReportCheck.cpp(1747,55): message : placeholders and their parameters expect 0 variadic arguments, but 1 were provided
  stdafx.cpp
