# NOTE: Requires **VS2019 16.7** or later

# 'MicrosoftCodeAnalysisLocalization' Rules from '3.3.0' release with 'Recommended' analysis mode escalated to 'error' severity
# Description: 'MicrosoftCodeAnalysisLocalization' Rules with enabled-by-default state from '3.3.0' release with 'Recommended' analysis mode. Rules that are first released in a version later than '3.3.0' are disabled. Enabled rules with 'warning' severity are escalated to 'error' severity to respect 'CodeAnalysisTreatWarningsAsErrors' MSBuild property.

is_global = true

global_level = -99

