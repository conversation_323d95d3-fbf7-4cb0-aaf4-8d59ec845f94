//#ifndef _LIB_TOUCH_LNV_H_INCLUDE_948228FV_85JI_2964_IE94_058276KL4A52
//#define _LIB_TOUCH_LNV_H_INCLUDE_948228FV_85JI_2964_IE94_058276KL4A52
#ifndef _LIB_TOUCH_H_INCLUDE_948228FV_85JI_2964_IE94_058276KL4A52
#define _LIB_TOUCH_H_INCLUDE_948228FV_85JI_2964_IE94_058276KL4A52 
//////////////////////////////////////////////////////////////
// Error code definition
//////////////////////////////////////////////////////////////

//** Function execution is success. **/
#ifndef TP_SUCCESS
#define TP_SUCCESS					0x0000	
#endif
/** This function is not support. **/
#ifndef TP_ERR_COMMAND_NOT_SUPPORT
#define TP_ERR_COMMAND_NOT_SUPPORT  0x0001
#endif
/** The touch ic may be occupied by other application **/
#ifndef TP_ERR_DEVICE_BUSY
#define TP_ERR_DEVICE_BUSY			0x0002
#endif
/** For asynchorous call, the execution is not finish yet. Waitting for complete **/
#ifndef TP_ERR_IO_PENDING
#define TP_ERR_IO_PENDING			0x0003 
#endif
/** Connect Elan Bridge and not get hello packet **/
#ifndef TP_ERR_CONNECT_NO_HELLO_PACKEY
#define TP_ERR_CONNECT_NO_HELLO_PACKEY		0x1002
#endif
/** Did not find any support device connectted to PC. Please check connectoin. **/
#ifndef TP_ERR_NOT_FOUND_DEVICE
#define TP_ERR_NOT_FOUND_DEVICE				0x1004
#endif
/** Test Mode Error Code **/
#ifndef TP_TESTMODE_GET_RAWDATA_FAIL
#define TP_TESTMODE_GET_RAWDATA_FAIL		0x3001			
#endif 
/** Error information check, use GetErrMsg to get the error message. **/
#ifndef TP_ERR_CHK_MSG
#define TP_ERR_CHK_MSG						0xFFFF
#endif 

//////////////////////////////////////////////////////////////
// Constant
//////////////////////////////////////////////////////////////
#ifndef MAX_TOUCH_REPORT_NUM
#define MAX_TOUCH_REPORT_NUM	100		// The maximum number of storing finger report
#endif
#ifndef MAX_TOUCH_REPORT_LEN
#define MAX_TOUCH_REPORT_LEN	116		// The maximum number of each HID report 
#endif

#pragma once
#include <Windows.h>
#include <string>
#include <vector>
#include <stdexcept>

using namespace std;
#ifdef ELANTPDLL_EXPORTS
#define ELANTPDLL_API extern "C" __declspec(dllexport)
#else
#define ELANTPDLL_API extern "C" __declspec(dllimport)
#endif


//#ifndef BOOL
/*******************************************************************
* Elan TP Library												  *
*******************************************************************
* Ver 0.0.1														  *							  
*******************************************************************/
////////////////////////////////////////////////////////////////////
// Since the pen report have different format by different firmware.
// The libraty will judge it first.
//

# pragma pack (1)

typedef struct _SYSDELTA_TIME {
   WORD wYear;
   WORD wMonth;
   WORD wDayOfWeek;
   WORD wDay;
   WORD wHour;
   WORD wMinute;
   WORD wSecond;
   WORD wMilliseconds;
   LONGLONG llDeltaMicroSeconds; 
} SYSDELTA_TIME;

//////////////////////////////////////////////////////////////////
// 
//
typedef struct _EMC_REPORT_PEN
{
	unsigned int uiReportID;

	bool bInRange;
	bool bTip;
	bool bBarrel;
	bool bInvert;

	bool bErase;
	bool bButton1;
	bool bButton2;
	bool bReserved;
	unsigned int uiBattery;
	unsigned int uiPosX;
	unsigned int uiPosY;
	unsigned int uiTipPressure;
}EMC_REPORT_PEN, *PEMC_REPORT_PEN;

////////////////////////////////////////////////////////////////////
// Since the pen report have different format by different firmware.
// The libraty will judge it first.
//
typedef struct _EMC_FINGER
{
	unsigned int uiContactID;
	bool bTip;
	bool bReserved[3];	
	unsigned int uiContactWidth;
	unsigned int uiContactHeight;
	unsigned int uiPosX;
	unsigned int uiPosY;
	unsigned int uiPosCenterX;
	unsigned int uiPosCenterY;

}EMC_FINGER, *PEMC_FINGER;

typedef struct _EMC_REPORT_FINGER
{
   unsigned int uiReportID;					
   unsigned int uiActiveTouchCount;   
   unsigned int uiScanTime;
   EMC_FINGER finger[10];			// 10 finger data
}EMC_REPORT_FINGER, *PEMC_REPORT_FINGER;


# pragma pack ()
typedef void (__stdcall *PFUNC_REPORT_RECEIVE)(unsigned char* pReportBuf, int nReportLen, int nReportCount, SYSDELTA_TIME timeStamp);
typedef struct _TOUCH_PEN_DATA
{
	BYTE ReportID;				// offset 0
	BYTE bInRange : 1;			// offset 1
	BYTE bTip : 1;
	BYTE bBarrel : 1;
	BYTE bInvert : 1;
	BYTE bErase : 1;
	BYTE reserve_offset_1 : 3;
	BYTE x_position_lsbyte;		// offset 2
	BYTE x_position_msbyte;		// offset 3
	BYTE y_position_lsbyte;		// offset 4
	BYTE y_position_msbyte;		// offset 5
	BYTE tip_pressure_lsbyte;	// offset 6
	BYTE tip_pressure_msbyte;	// offset 7
} TOUCH_PEN_DATA, *PTOUCH_PEN_DATA;

typedef struct _TOUCH_FINGER
{
	BYTE bTip : 1;
	BYTE reserve_offset : 3;		
	BYTE contactID : 4;					// offset 0
	BYTE contact_width;					// offset 1
	BYTE contact_height;				// offset 2
	BYTE x_position_lsbyte;				// offset 3
	BYTE x_position_msbyte;				// offset 4
	BYTE x_center_position_lsbyte;		// offset 5
	BYTE x_center_position_msbyte;		// offset 6
	BYTE y_position_lsbyte;				// offset 7
	BYTE y_position_msbyte;				// offset 8
	BYTE y_center_position_lsbyte;		// offset 9
	BYTE y_center_position_msbyte;		// offset 10

}TOUCH_FINGER, *PTOUCH_FINGER;

typedef struct _TOUCH_FINGER_DATA
{
   BYTE ReportID;					// 0ffset 0
   TOUCH_FINGER finger[10];			// 0ffset 1 ~ 110
   BYTE scan_time_low_lsbyte;		// offset 111
   BYTE scan_time_low_msbyte;		// offset 112
   BYTE scan_time_high_lsbyte;		// offset 113
   BYTE scan_time_high_msbyte;		// offset 114
   BYTE active_touch_count;			// offset 115
}TOUCH_FINGER_DATA, *PTOUCH_FINGER_DATA;

# pragma pack ()


# pragma pack (1)

typedef struct _EXPORT_VALUE_CAPS
{
	int iLocialMax;
	int iUsage;
	int iUsagePage;
}EXPORT_VALUE_CAPS, *PEXPORT_VALUE_CAPS;

typedef struct _EXPORT_PEN_PROFILE
{
	int ReportID;

	EXPORT_VALUE_CAPS	CapsLogicalX;
	EXPORT_VALUE_CAPS	CapsLogicalY;
	EXPORT_VALUE_CAPS	CapsTipPressure;

	int inputReportLength;				// Pen Report Length
}EXPORT_PEN_PROFILE, *PEXPORT_PEN_PROFILE;

//
// HID device "Finger" information
// 
typedef struct _EXPORT_FINGER_PROFILE
{
	//m_nContactNumIdx = m_nContactMaxNumEachReport * m_nContactDataSize + m_nScanTimeSize + 1;	
	int nContactNumIdx;
	//
	// HID device information
	// 
	int ReportID;
	int maxLogicalX;
	int maxLogicalY;	
	int maxContactCnt;					// 10 fingers max. 
	int inputReportLength;				// Touch report length is 116 bytes.

}EXPORT_FINGER_PROFILE, *PEXPORT_FINGER_PROFILE;


// EMC_PROFILE_TOUCH
typedef struct _EXPORT_TOUCH_PROFILE_
{

	int				nDevIdx;				
	bool			bSupportFinger;
	bool			bSupportPen;

	EXPORT_FINGER_PROFILE		ProfFinger;
	EXPORT_PEN_PROFILE			ProfPen;

}EXPORT_TOUCH_PROFILE, *PEXPORT_TOUCH_PROFILE;

# pragma pack ()

namespace ELANAPI {

////////////////////////////////////////////////////////
// Debug and Error message functions
////////////////////////////////////////////////////////

// When API function call get error code "TP_ERR_CHK_MSG". Use this 
// function to get more error information.
ELANTPDLL_API char* __stdcall GetErrMessage(void);

////////////////////////////////////////////////////////
//Declare functions : HID basic funcitons
////////////////////////////////////////////////////////

//nInterface
// Windows:
//	-INTF_TYPE_HID_WINDOWS			= 1;
//
// Linux:
//	-INTF_TYPE_HID_LINUX			= 2;
//	-INTF_TYPE_I2C_LINUX			= 3;
//	-INTF_TYPE_I2CHID_LINUX			= 4;
//	-INTF_TYPE_I2C_CHROME_LINUX		= 5;
//	-INTF_TYPE_FW20_I2C_LINUX		= 6;
ELANTPDLL_API int __stdcall Connect(int nVID = 0x04F3, 
									int nPID = 0x00, 
									int nInterface=1, //Default is Windows HID
									int nVdd=33, 
									int nVio=33, 
									int nI2CAdr=0x20);

/*!
	Close the connection wiht HID Device
*/
ELANTPDLL_API int __stdcall Disconnect();

ELANTPDLL_API int __stdcall GetProfileExport(PEXPORT_TOUCH_PROFILE pProfile, int nDevIdx = 0);

ELANTPDLL_API int __stdcall InputRawRegHIDCallback(PFUNC_REPORT_RECEIVE pFuncReportRcv, int nDevIdx = 0, int nUsagePage = 0x0D, int Usage = 0x00);
ELANTPDLL_API int __stdcall InputRawUnRegHIDCallback(int nDevIdx = 0);



ELANTPDLL_API int __stdcall ParserReportPen(unsigned char *pData, int iDataLen, PEMC_REPORT_PEN p_rpPen, int nDevIdx = 0);
ELANTPDLL_API int __stdcall ParserReportFinger(unsigned char *pData, int iDataLen, PEMC_REPORT_FINGER p_rpFinger );
////////////////////////////////////////////////////////
// Send command 
////////////////////////////////////////////////////////
ELANTPDLL_API int __stdcall SendDevCommand(unsigned char *pszCommandBuf, int nCommandLen,  int nTimeout = -1, int nDevIdx = 0);

ELANTPDLL_API int __stdcall ReadDevData(unsigned char *pszDataBuf,int nDataLen, int nTimeoutMS = -1, int nDevIdx = 0);

}
#endif // _LIB_TOUCH_LNV_H_INCLUDE_948228FV_85JI_2964_IE94_058276KL4A52