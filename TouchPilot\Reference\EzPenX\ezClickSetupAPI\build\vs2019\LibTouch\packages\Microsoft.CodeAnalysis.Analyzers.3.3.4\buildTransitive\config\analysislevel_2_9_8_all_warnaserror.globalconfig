# NOTE: Requires **VS2019 16.7** or later

# Rules from '2.9.8' release with 'All' analysis mode escalated to 'error' severity
# Description: Rules with enabled-by-default state from '2.9.8' release with 'All' analysis mode. Rules that are first released in a version later than '2.9.8' are disabled. Enabled rules with 'warning' severity are escalated to 'error' severity to respect 'CodeAnalysisTreatWarningsAsErrors' MSBuild property.

is_global = true

global_level = -100


# RS1001: Missing diagnostic analyzer attribute
dotnet_diagnostic.RS1001.severity = error

# RS1002: Missing kind argument when registering an analyzer action
dotnet_diagnostic.RS1002.severity = error

# RS1003: Unsupported SymbolKind argument when registering a symbol analyzer action
dotnet_diagnostic.RS1003.severity = error

# RS1004: Recommend adding language support to diagnostic analyzer
dotnet_diagnostic.RS1004.severity = error

# RS1005: ReportDiagnostic invoked with an unsupported DiagnosticDescriptor
dotnet_diagnostic.RS1005.severity = error

# RS1006: Invalid type argument for DiagnosticAnalyzer's Register method
dotnet_diagnostic.RS1006.severity = error

# RS1008: Avoid storing per-compilation data into the fields of a diagnostic analyzer
dotnet_diagnostic.RS1008.severity = error

# RS1010: Create code actions should have a unique EquivalenceKey for FixAll occurrences support
dotnet_diagnostic.RS1010.severity = error

# RS1011: Use code actions that have a unique EquivalenceKey for FixAll occurrences support
dotnet_diagnostic.RS1011.severity = error

# RS1012: Start action has no registered actions
dotnet_diagnostic.RS1012.severity = error

# RS1013: Start action has no registered non-end actions
dotnet_diagnostic.RS1013.severity = error

# RS1014: Do not ignore values returned by methods on immutable objects
dotnet_diagnostic.RS1014.severity = error

# RS1016: Code fix providers should provide FixAll support
dotnet_diagnostic.RS1016.severity = error

# RS1017: DiagnosticId for analyzers must be a non-null constant
dotnet_diagnostic.RS1017.severity = error

# RS1018: DiagnosticId for analyzers must be in specified format
dotnet_diagnostic.RS1018.severity = error

# RS1019: DiagnosticId must be unique across analyzers
dotnet_diagnostic.RS1019.severity = error

# RS1021: Invalid entry in analyzer category and diagnostic ID range specification file
dotnet_diagnostic.RS1021.severity = error

# RS1022: Do not use types from Workspaces assembly in an analyzer
dotnet_diagnostic.RS1022.severity = error

# RS1023: Upgrade MSBuildWorkspace
dotnet_diagnostic.RS1023.severity = error

# RS1024: Symbols should be compared for equality
dotnet_diagnostic.RS1024.severity = none

# RS1025: Configure generated code analysis
dotnet_diagnostic.RS1025.severity = none

# RS1026: Enable concurrent execution
dotnet_diagnostic.RS1026.severity = none

# RS1027: Types marked with DiagnosticAnalyzerAttribute(s) should inherit from DiagnosticAnalyzer
dotnet_diagnostic.RS1027.severity = none

# RS1029: Do not use reserved diagnostic IDs
dotnet_diagnostic.RS1029.severity = none

# RS1030: Do not invoke Compilation.GetSemanticModel() method within a diagnostic analyzer
dotnet_diagnostic.RS1030.severity = none

# RS1031: Define diagnostic title correctly
dotnet_diagnostic.RS1031.severity = none

# RS1032: Define diagnostic message correctly
dotnet_diagnostic.RS1032.severity = none

# RS1033: Define diagnostic description correctly
dotnet_diagnostic.RS1033.severity = none

# RS1034: Prefer 'IsKind' for checking syntax kinds
dotnet_diagnostic.RS1034.severity = none

# RS1035: Do not use APIs banned for analyzers
dotnet_diagnostic.RS1035.severity = none

# RS1036: Specify analyzer banned API enforcement setting
dotnet_diagnostic.RS1036.severity = none

# RS1037: Add "CompilationEnd" custom tag to compilation end diagnostic descriptor
dotnet_diagnostic.RS1037.severity = none

# RS2000: Add analyzer diagnostic IDs to analyzer release
dotnet_diagnostic.RS2000.severity = none

# RS2001: Ensure up-to-date entry for analyzer diagnostic IDs are added to analyzer release
dotnet_diagnostic.RS2001.severity = none

# RS2002: Do not add removed analyzer diagnostic IDs to unshipped analyzer release
dotnet_diagnostic.RS2002.severity = none

# RS2003: Shipped diagnostic IDs that are no longer reported should have an entry in the 'Removed Rules' table in unshipped file
dotnet_diagnostic.RS2003.severity = none

# RS2004: Diagnostic IDs marked as removed in analyzer release file should not be reported by analyzers
dotnet_diagnostic.RS2004.severity = none

# RS2005: Remove duplicate entries for diagnostic ID in the same analyzer release
dotnet_diagnostic.RS2005.severity = none

# RS2006: Remove duplicate entries for diagnostic ID between analyzer releases
dotnet_diagnostic.RS2006.severity = none

# RS2007: Invalid entry in analyzer release file
dotnet_diagnostic.RS2007.severity = none

# RS2008: Enable analyzer release tracking
dotnet_diagnostic.RS2008.severity = none
