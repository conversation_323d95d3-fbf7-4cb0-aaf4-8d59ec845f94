# NOTE: Requires **VS2019 16.7** or later

# Rules from '4.3' release with 'Minimum' analysis mode
# Description: Rules with enabled-by-default state from '4.3' release with 'Minimum' analysis mode. Rules that are first released in a version later than '4.3' are disabled.

is_global = true

global_level = -100


# RS1009: Only internal implementations of this interface are allowed
dotnet_diagnostic.RS1009.severity = warning

# RS1035: Do not use APIs banned for analyzers
dotnet_diagnostic.RS1035.severity = warning
